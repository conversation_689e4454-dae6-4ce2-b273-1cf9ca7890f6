2025-06-13 15:35:21 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:35:21 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:35:21 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:35:21 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:35:21 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.feedexport.FeedExporter',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:35:21 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:35:21 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:35:21 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:35:21 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:35:21 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:35:21 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 15:35:21 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:35:21 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:35:21 [product_view] INFO: Spider opened: product_view
2025-06-13 15:35:21 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:35:22 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:35:22 [product_view] INFO: Exported 1 items to output/product_view_20250613_153521.json
2025-06-13 15:35:22 [scrapy.extensions.feedexport] INFO: Stored json feed (1 items) in: product_view_test.json
2025-06-13 15:35:22 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 350,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 74072,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.793412,
 'feedexport/success_count/FileFeedStorage': 1,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 35, 22, 757867, tzinfo=datetime.timezone.utc),
 'httpcache/firsthand': 1,
 'httpcache/miss': 1,
 'httpcache/store': 1,
 'httpcompression/response_bytes': 306055,
 'httpcompression/response_count': 1,
 'item_scraped_count': 1,
 'items_per_minute': None,
 'log_count/INFO': 11,
 'log_count/WARNING': 4,
 'memusage/max': 75452416,
 'memusage/startup': 75452416,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'start_time': datetime.datetime(2025, 6, 13, 13, 35, 21, 964455, tzinfo=datetime.timezone.utc)}
2025-06-13 15:35:22 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 15:39:18 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:39:18 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:39:18 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:39:18 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:39:18 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.feedexport.FeedExporter',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:39:18 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:39:18 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:39:18 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:39:18 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:39:18 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:39:18 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 15:39:18 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:39:18 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:39:18 [product_view] INFO: Spider opened: product_view
2025-06-13 15:39:18 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:39:19 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:39:19 [product_view] INFO: Exported 1 items to output/product_view_20250613_153918.json
2025-06-13 15:39:19 [scrapy.extensions.feedexport] INFO: Stored json feed (1 items) in: product_view_test.json
2025-06-13 15:39:19 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 340,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 74072,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.676962,
 'feedexport/success_count/FileFeedStorage': 1,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 39, 19, 269008, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 1,
 'httpcompression/response_bytes': 306055,
 'httpcompression/response_count': 1,
 'item_scraped_count': 1,
 'items_per_minute': None,
 'log_count/INFO': 11,
 'log_count/WARNING': 4,
 'memusage/max': 75579392,
 'memusage/startup': 75579392,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'start_time': datetime.datetime(2025, 6, 13, 13, 39, 18, 592046, tzinfo=datetime.timezone.utc)}
2025-06-13 15:39:19 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 15:39:36 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:39:36 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:39:36 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:39:36 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:39:36 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.feedexport.FeedExporter',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:39:36 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:39:36 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:39:36 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:39:36 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:39:36 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:39:36 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 15:39:36 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:39:36 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:39:36 [product_view] INFO: Spider opened: product_view
2025-06-13 15:39:36 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:39:36 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:39:36 [product_view] INFO: Exported 1 items to output/product_view_20250613_153936.json
2025-06-13 15:39:37 [scrapy.extensions.feedexport] INFO: Stored json feed (1 items) in: product_view_latest.json
2025-06-13 15:39:37 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 340,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 74072,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.633908,
 'feedexport/success_count/FileFeedStorage': 1,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 39, 37, 35, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 1,
 'httpcompression/response_bytes': 306055,
 'httpcompression/response_count': 1,
 'item_scraped_count': 1,
 'items_per_minute': None,
 'log_count/INFO': 11,
 'log_count/WARNING': 4,
 'memusage/max': 75575296,
 'memusage/startup': 75575296,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'start_time': datetime.datetime(2025, 6, 13, 13, 39, 36, 366127, tzinfo=datetime.timezone.utc)}
2025-06-13 15:39:37 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 15:41:50 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:41:50 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:41:50 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:41:50 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:41:50 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.feedexport.FeedExporter',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:41:50 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:41:50 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:41:50 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:41:50 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:41:50 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:41:50 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 15:41:50 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:41:50 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:41:50 [product_view] INFO: Spider opened: product_view
2025-06-13 15:41:50 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:41:50 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:41:50 [product_view] INFO: Exported 1 items to output/product_view_20250613_154150.json
2025-06-13 15:41:50 [scrapy.extensions.feedexport] INFO: Stored json feed (1 items) in: product_view_final.json
2025-06-13 15:41:50 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 313,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 74072,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.587855,
 'feedexport/success_count/FileFeedStorage': 1,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 41, 50, 855926, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 1,
 'httpcompression/response_bytes': 306055,
 'httpcompression/response_count': 1,
 'item_scraped_count': 1,
 'items_per_minute': None,
 'log_count/INFO': 11,
 'log_count/WARNING': 4,
 'memusage/max': 75579392,
 'memusage/startup': 75579392,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'start_time': datetime.datetime(2025, 6, 13, 13, 41, 50, 268071, tzinfo=datetime.timezone.utc)}
2025-06-13 15:41:50 [scrapy.core.engine] INFO: Spider closed (finished)
