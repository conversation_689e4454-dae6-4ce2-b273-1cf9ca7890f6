2025-06-13 15:35:21 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:35:21 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:35:21 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:35:21 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:35:21 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.feedexport.FeedExporter',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:35:21 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:35:21 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:35:21 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:35:21 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:35:21 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:35:21 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 15:35:21 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:35:21 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:35:21 [product_view] INFO: Spider opened: product_view
2025-06-13 15:35:21 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:35:22 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:35:22 [product_view] INFO: Exported 1 items to output/product_view_20250613_153521.json
2025-06-13 15:35:22 [scrapy.extensions.feedexport] INFO: Stored json feed (1 items) in: product_view_test.json
2025-06-13 15:35:22 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 350,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 74072,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.793412,
 'feedexport/success_count/FileFeedStorage': 1,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 35, 22, 757867, tzinfo=datetime.timezone.utc),
 'httpcache/firsthand': 1,
 'httpcache/miss': 1,
 'httpcache/store': 1,
 'httpcompression/response_bytes': 306055,
 'httpcompression/response_count': 1,
 'item_scraped_count': 1,
 'items_per_minute': None,
 'log_count/INFO': 11,
 'log_count/WARNING': 4,
 'memusage/max': 75452416,
 'memusage/startup': 75452416,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'start_time': datetime.datetime(2025, 6, 13, 13, 35, 21, 964455, tzinfo=datetime.timezone.utc)}
2025-06-13 15:35:22 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 15:39:18 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:39:18 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:39:18 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:39:18 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:39:18 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.feedexport.FeedExporter',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:39:18 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:39:18 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:39:18 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:39:18 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:39:18 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:39:18 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 15:39:18 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:39:18 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:39:18 [product_view] INFO: Spider opened: product_view
2025-06-13 15:39:18 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:39:19 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:39:19 [product_view] INFO: Exported 1 items to output/product_view_20250613_153918.json
2025-06-13 15:39:19 [scrapy.extensions.feedexport] INFO: Stored json feed (1 items) in: product_view_test.json
2025-06-13 15:39:19 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 340,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 74072,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.676962,
 'feedexport/success_count/FileFeedStorage': 1,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 39, 19, 269008, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 1,
 'httpcompression/response_bytes': 306055,
 'httpcompression/response_count': 1,
 'item_scraped_count': 1,
 'items_per_minute': None,
 'log_count/INFO': 11,
 'log_count/WARNING': 4,
 'memusage/max': 75579392,
 'memusage/startup': 75579392,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'start_time': datetime.datetime(2025, 6, 13, 13, 39, 18, 592046, tzinfo=datetime.timezone.utc)}
2025-06-13 15:39:19 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 15:39:36 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:39:36 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:39:36 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:39:36 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:39:36 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.feedexport.FeedExporter',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:39:36 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:39:36 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:39:36 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:39:36 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:39:36 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:39:36 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 15:39:36 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:39:36 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:39:36 [product_view] INFO: Spider opened: product_view
2025-06-13 15:39:36 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:39:36 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:39:36 [product_view] INFO: Exported 1 items to output/product_view_20250613_153936.json
2025-06-13 15:39:37 [scrapy.extensions.feedexport] INFO: Stored json feed (1 items) in: product_view_latest.json
2025-06-13 15:39:37 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 340,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 74072,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.633908,
 'feedexport/success_count/FileFeedStorage': 1,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 39, 37, 35, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 1,
 'httpcompression/response_bytes': 306055,
 'httpcompression/response_count': 1,
 'item_scraped_count': 1,
 'items_per_minute': None,
 'log_count/INFO': 11,
 'log_count/WARNING': 4,
 'memusage/max': 75575296,
 'memusage/startup': 75575296,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'start_time': datetime.datetime(2025, 6, 13, 13, 39, 36, 366127, tzinfo=datetime.timezone.utc)}
2025-06-13 15:39:37 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 15:41:50 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:41:50 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:41:50 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:41:50 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:41:50 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.feedexport.FeedExporter',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:41:50 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:41:50 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:41:50 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:41:50 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:41:50 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:41:50 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 15:41:50 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:41:50 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:41:50 [product_view] INFO: Spider opened: product_view
2025-06-13 15:41:50 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:41:50 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:41:50 [product_view] INFO: Exported 1 items to output/product_view_20250613_154150.json
2025-06-13 15:41:50 [scrapy.extensions.feedexport] INFO: Stored json feed (1 items) in: product_view_final.json
2025-06-13 15:41:50 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 313,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 74072,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.587855,
 'feedexport/success_count/FileFeedStorage': 1,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 41, 50, 855926, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 1,
 'httpcompression/response_bytes': 306055,
 'httpcompression/response_count': 1,
 'item_scraped_count': 1,
 'items_per_minute': None,
 'log_count/INFO': 11,
 'log_count/WARNING': 4,
 'memusage/max': 75579392,
 'memusage/startup': 75579392,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'start_time': datetime.datetime(2025, 6, 13, 13, 41, 50, 268071, tzinfo=datetime.timezone.utc)}
2025-06-13 15:41:50 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 15:53:20 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:53:20 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:53:20 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:53:20 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:53:20 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.feedexport.FeedExporter',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:53:20 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:53:20 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:53:20 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:53:20 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:53:20 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:53:20 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 15:53:20 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:53:20 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:53:20 [review] INFO: Spider opened: review
2025-06-13 15:53:20 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:53:20 [review] INFO: Product 118805 has 153 reviews across 8 pages
2025-06-13 15:53:21 [review] INFO: Parsing reviews page 1 for product 118805
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 11:50',
 'review_has_answer': False,
 'review_id': '38766955',
 'reviewer_id': 'daryanahmmed',
 'reviewer_name': 'Daryan Ahmed',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:53:21.044140',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 04:27',
 'review_has_answer': False,
 'review_id': '38826636',
 'reviewer_id': 'chalermpetchara',
 'reviewer_name': 'chalerm petcharat',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:53:21.047959',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '599 Pitt Petruschke 2025.06.11 09:21 # ich nutze den EA '
                   'jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das '
                   'bedeutet, der macht wenige aber dafür profitabele Trades, '
                   'was meinem Trading Stil entgegenkommt. Der Support '
                   'arbeitet super schnell...',
 'review_datetime': '2025.06.11 09:21',
 'review_has_answer': False,
 'review_id': '38655413',
 'reviewer_id': 'pablo1904',
 'reviewer_name': 'Pitt Petruschke',
 'reviewer_score': 599,
 'scraped_at': '2025-06-13T15:53:21.050102',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [review] ERROR: Error extracting review data: 'SelectorList' object has no attribute 'first'
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '36 Kirilka59 2025.06.11 08:19 # I decided to leave a '
                   'review after 2-3 weeks of testing on a live account. I '
                   'really like the way QQ5 works. Deals are opened not very '
                   'often, but qualitatively and I understand that this is due '
                   'to the fact that EA waits for the perfect moment, taking '
                   'into account the market situation. And when it opens a '
                   'deal, then another one and another and eventually closes '
                   'almost all deals with a profit. I have never had a series '
                   'of trades that went into the minus, always in the plus. My '
                   'settings are automatic with medium risk. My broker is '
                   "ByBit. I am very happy with the result of QQ5's work, I "
                   'will continue to trust him to manage my budget. Thank you '
                   'for such a quality EA! I recommend everyone to buy it, the '
                   'price matches the quality - and it will definitely pay '
                   'off!',
 'review_datetime': '2025.06.11 08:19',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'kirilka59',
 'reviewer_name': 'Kirilka59',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:53:21.054404',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '48 goateeeth maker 2025.06.08 02:25 # ⭐⭐⭐⭐⭐ Excellent '
                   "EA—Profitable and Reliable! I've been using this EA on MT5 "
                   "for several weeks, and it's exceeded my expectations. The "
                   'performance has been consistent, generating steady profits '
                   'while effectively managing risks. Setup was '
                   'straightforward, and customer support has been responsive '
                   'whenever I had questions. Highly recommend this EA to '
                   'anyone looking for an automated, stress-free trading '
                   'solution!',
 'review_datetime': '2025.06.08 02:25',
 'review_has_answer': False,
 'review_id': '38333720',
 'reviewer_id': 'goateeeth',
 'reviewer_name': 'goateeeth maker',
 'reviewer_score': 48,
 'scraped_at': '2025-06-13T15:53:21.056616',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '79 Saman83 2025.06.06 23:02 # 🌟 An Expert Advisor That '
                   'Truly Delivers! 🌟 I’ve been using Quantum Queen MT5 for a '
                   'short time, but I’m already seeing my account grow '
                   'steadily. A colleague of mine has been using it for over '
                   'two months, and his results have been exceptional and '
                   'consistent, without hesitation. 🔹 It’s one of the very few '
                   'algobots on MetaTrader 5 that actually works. 🔹 The '
                   'developer is very professional and always quick to respond '
                   'to any questions. 🔹 It was recommended to me by someone '
                   'extremely meticulous… and it has exceeded all '
                   'expectations. 💰 It’s worth every cent of the price. This '
                   'is a professional tool, not just another empty promise. 🔒 '
                   'Reliable, profitable, and well-supported. Highly '
                   'recommended!',
 'review_datetime': '2025.06.06 23:02',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'saman83',
 'reviewer_name': 'Saman83',
 'reviewer_score': 79,
 'scraped_at': '2025-06-13T15:53:21.058543',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '53 SAMUEL DE SOUZA 2025.06.06 22:12 # Absolutely '
                   'Phenomenal – A True Queen in the Game! I’ve tested over 40 '
                   'different AEs in my trading journey, and I can confidently '
                   'say the Quantum Queen MT5 is in a league of its own. It’s '
                   'not just another gold (XAUUSD) AE—it’s a '
                   'precision-engineered powerhouse. From setup to execution, '
                   'everything runs smoothly and efficiently, adapting '
                   'perfectly to changing market conditions. Alongside The '
                   'Emperor, Quantum Queen is easily one of the two best AEs '
                   'I’ve ever come across. The consistency, reliability, and '
                   'intelligent trade management this bot brings are '
                   'unmatched. Whether you’re a seasoned trader or just '
                   'starting out, this is a must-have for your arsenal. Highly '
                   'recommend to anyone serious about mastering gold on MT5!',
 'review_datetime': '2025.06.06 22:12',
 'review_has_answer': False,
 'review_id': '38737428',
 'reviewer_id': 'swtsam2',
 'reviewer_name': 'SAMUEL DE SOUZA',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:53:21.060726',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '22 Noalc 2025.06.06 20:33 # 购买这个EA之后我基于最近半年短期做了很多次回测 '
                   '根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 每一次都是盈利的 而且都是非常可观的 '
                   '用我们国家的一句老话来说就是 兵欲善其事 必先利其器 我觉得QQ对我这个新手来说十分友好 '
                   '并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的',
 'review_datetime': '2025.06.06 20:33',
 'review_has_answer': False,
 'review_id': '38705502',
 'reviewer_id': 'noalc',
 'reviewer_name': 'Noalc',
 'reviewer_score': 22,
 'scraped_at': '2025-06-13T15:53:21.062730',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [review] ERROR: Error extracting review data: 'SelectorList' object has no attribute 'first'
2025-06-13 15:53:21 [review] ERROR: Error extracting review data: 'SelectorList' object has no attribute 'first'
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "66 Dnamron 2025.06.06 02:24 # Bonjour je viens d'acheter "
                   "expert Quantum Queen MT5 c'est un très bon expert il est "
                   "très stable il n'y a des perte que très minime et donne "
                   'plus de bon profit je le trouve excellent. Très bon '
                   "service après vente pour l'installation et les conseils de "
                   'Bogdan Ion Puscasu il a répondu à mes demandes et à mes '
                   'messages avec une rapidité exemplaire je suis très '
                   'satisfait merci beaucoup.',
 'review_datetime': '2025.06.06 02:24',
 'review_has_answer': False,
 'review_id': '38679597',
 'reviewer_id': 'dnamron',
 'reviewer_name': 'Dnamron',
 'reviewer_score': 66,
 'scraped_at': '2025-06-13T15:53:21.066776',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '19 jza80spl 2025.06.04 08:33 # "This is an EA that allows '
                   'you to trade with great peace of mind." 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:43 # Thank you '
                   'so much! That’s exactly what I wanted to achieve with '
                   'Quantum Queen — an EA that brings not just results, but '
                   'also peace of mind. Trading shouldn’t be stressful, and '
                   'I’m really glad you’re feeling that sense of calm while '
                   'using it. If you ever have questions or need anything, '
                   'feel free to reach out anytime!',
 'review_datetime': '2025.06.04 08:33',
 'review_has_answer': False,
 'review_id': '38661377',
 'reviewer_id': 'jza80spl',
 'reviewer_name': 'jza80spl',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:53:21.069624',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '378 Guorong He 2025.06.03 16:06 # Very good grid system. '
                   'Thanks Bogdan！ 34750 Reply from developer Bogdan Ion '
                   'Puscasu 2025.06.05 14:42 # Thank you so much for your '
                   "review! I’m really glad to hear you're enjoying the grid "
                   'system. It’s designed to be both smart and steady, and I’m '
                   'happy it’s working well for you. Appreciate the kind words '
                   '– and if you ever need help or want to explore more '
                   'Quantum EAs, I’m just a message away!',
 'review_datetime': '2025.06.03 16:06',
 'review_has_answer': False,
 'review_id': '38691977',
 'reviewer_id': 'tokio147',
 'reviewer_name': 'Guorong He',
 'reviewer_score': 378,
 'scraped_at': '2025-06-13T15:53:21.074506',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [review] ERROR: Error extracting review data: 'SelectorList' object has no attribute 'first'
2025-06-13 15:53:21 [review] ERROR: Error extracting review data: 'SelectorList' object has no attribute 'first'
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.02 21:18',
 'review_has_answer': False,
 'review_id': '38596644',
 'reviewer_id': 'molobamllr',
 'reviewer_name': 'Lorenz Goeminne',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:53:21.085568',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '785 Nasoo 2025.06.02 13:36 # Bought the EA last week and '
                   'already made about 4% in just one week! Really happy with '
                   'the performance so far. The documentation provided is very '
                   'detailed and informative — it made setup and understanding '
                   'the strategy very easy. Looking forward to seeing more '
                   'great results. 34750 Reply from developer Bogdan Ion '
                   "Puscasu 2025.06.05 14:37 # That's fantastic to hear! I'm "
                   'really glad the setup process went smoothly and that '
                   "you're already seeing solid results in just one week — 4% "
                   'is a great start! Thanks for pointing out the '
                   'documentation too, I put a lot of effort into making it as '
                   'clear and helpful as possible. Wishing you continued '
                   'success, and if you ever need anything, don’t hesitate to '
                   'reach out.',
 'review_datetime': '2025.06.02 13:36',
 'review_has_answer': False,
 'review_id': '38600643',
 'reviewer_id': 'nasoo',
 'reviewer_name': 'Nasoo',
 'reviewer_score': 785,
 'scraped_at': '2025-06-13T15:53:21.091381',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [review] ERROR: Error extracting review data: 'SelectorList' object has no attribute 'first'
2025-06-13 15:53:21 [review] ERROR: Error extracting review data: 'SelectorList' object has no attribute 'first'
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '34 Francesc00 2025.06.01 15:52 # I am very satisfied with '
                   'the results from the 1st week, its the same as in the '
                   'backtests. I fully recommend it. 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:35 # Thank you '
                   "so much for the great feedback! I'm really glad to hear "
                   'that your first week with Quantum Queen matches the '
                   "backtests — that's always a great sign. It means a lot to "
                   "know you're satisfied and confident in the EA. Wishing you "
                   'continued success and steady profits ahead! If you ever '
                   "need help or have questions, I'm here.",
 'review_datetime': '2025.06.01 15:52',
 'review_has_answer': False,
 'review_id': '38580848',
 'reviewer_id': 'francesc00',
 'reviewer_name': 'Francesc00',
 'reviewer_score': 34,
 'scraped_at': '2025-06-13T15:53:21.099964',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.05.31 06:14',
 'review_has_answer': False,
 'review_id': '38668358',
 'reviewer_id': 'schnitzler88',
 'reviewer_name': 'Extoflex',
 'reviewer_score': 328,
 'scraped_at': '2025-06-13T15:53:21.105846',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [review] ERROR: Error extracting review data: 'SelectorList' object has no attribute 'first'
2025-06-13 15:53:21 [review] ERROR: Error extracting review data: 'SelectorList' object has no attribute 'first'
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '40 Lucas Ramos 2025.05.30 09:43 # O Bogdan é um Trader '
                   'extremamente responsável e simpático e oferece suporte '
                   'rápido, a Quantum Queen é uma ótima EA que já me rendeu '
                   'bons lucros com baixos riscos, recomendo fortemente o uso '
                   'dessa EA para Traders responsáveis e que queiram fazer seu '
                   'dinheiro render sem muito esforço 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:33 # Thank you '
                   "so much for your kind words and trust, Lucas! 🙏 I'm really "
                   'happy to hear that Quantum Queen is delivering good '
                   'profits with low risk for you — exactly what I designed it '
                   'for. I truly appreciate your support and recommendation, '
                   "and I'm always here if you need help or have any "
                   'questions. Wishing you continued success and steady growth '
                   "— let's keep making the most out of every trade!",
 'review_datetime': '2025.05.30 09:43',
 'review_has_answer': False,
 'review_id': '38537336',
 'reviewer_id': 'lucasramoss',
 'reviewer_name': 'Lucas Ramos',
 'reviewer_score': 40,
 'scraped_at': '2025-06-13T15:53:21.112695',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '56 Leo 2025.05.30 08:56 # Bogdan has been incredibly '
                   'responsive, often replying within minutes, which makes it '
                   'feel like I have a tech team right beside me — something I '
                   'really appreciate. I’ve been running the EA from May 26th '
                   'to May 29th, and it delivered a 2% profit within just four '
                   'days using the default risk settings. It doesn’t use a '
                   'martingale strategy, but it does attempt to average price '
                   'levels in a controlled manner. I especially like that it '
                   'only executes trades when specific market conditions are '
                   'met, aiming for higher probability setups. So far, the '
                   'performance has been solid and feels safe. Highly '
                   'recommended, and based on these results, I plan to explore '
                   'more of their products. 34750 Reply from developer Bogdan '
                   'Ion Puscasu 2025.06.05 14:31 # Thank you so much for the '
                   'thoughtful and detailed feedback, Leo! 🙏 I’m really glad '
                   'to hear that you appreciate the way Quantum Queen handles '
                   'trades — focused, careful, and without martingale risk. '
                   'I’ve spent a lot of time fine-tuning the strategy to be '
                   'both smart and safe, and it’s great to know it’s '
                   'performing well for you. I’m also always happy to help, so '
                   'don’t hesitate to reach out anytime. Looking forward to '
                   'having you explore the rest of the Quantum family — '
                   'there’s more good stuff waiting!',
 'review_datetime': '2025.05.30 08:56',
 'review_has_answer': False,
 'review_id': '38237707',
 'reviewer_id': 'chasuhun23',
 'reviewer_name': 'Leo',
 'reviewer_score': 56,
 'scraped_at': '2025-06-13T15:53:21.118244',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [review] ERROR: Error extracting review data: 'SelectorList' object has no attribute 'first'
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '24 Michael Schnitzer 2025.05.29 17:57 # I can highly '
                   'recommend the Queen. You get an immediate response to any '
                   'kind of question, and the system truly works. Thank you, '
                   'Bogdan! 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:29 # Thank you so much for the wonderful '
                   'feedback! I’m really happy to hear that Quantum Queen is '
                   'working well for you and that you feel supported — I’m '
                   'always here to help whenever you need. Wishing you '
                   'continued success and steady profits!',
 'review_datetime': '2025.05.29 17:57',
 'review_has_answer': False,
 'review_id': '38508546',
 'reviewer_id': 'm_schnitzer',
 'reviewer_name': 'Michael Schnitzer',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:53:21.125175',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '35 Steels is 2025.05.29 12:33 # "It is a great system and '
                   'a profitable, thank you Bogdan! 34750 Reply from developer '
                   'Bogdan Ion Puscasu 2025.06.05 14:28 # Thank you so much '
                   'for your kind words! I’m really glad to hear you’re seeing '
                   'great results with the system. That’s exactly what I aimed '
                   'for when building Quantum Queen. Your support means a lot '
                   '— thank you!',
 'review_datetime': '2025.05.29 12:33',
 'review_has_answer': False,
 'review_id': '38517999',
 'reviewer_id': 'steelis',
 'reviewer_name': 'Steels is',
 'reviewer_score': 35,
 'scraped_at': '2025-06-13T15:53:21.130784',
 'timestamp_extraction_review': '2025-06-13 15:53',
 'url': 'https://www.mql5.com/en/market/product/118805?source=Site+Market+MT5+Rating006'}
2025-06-13 15:53:21 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:53:21 [review] INFO: Exported 1 items to output/review_20250613_155320.json
2025-06-13 15:53:21 [scrapy.extensions.feedexport] INFO: Stored json feed (1 items) in: review_test.json
2025-06-13 15:53:21 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 991,
 'downloader/request_count': 2,
 'downloader/request_method_count/GET': 2,
 'downloader/response_bytes': 148302,
 'downloader/response_count': 2,
 'downloader/response_status_count/200': 2,
 'dupefilter/filtered': 7,
 'elapsed_time_seconds': 0.597506,
 'feedexport/success_count/FileFeedStorage': 1,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 53, 21, 138271, tzinfo=datetime.timezone.utc),
 'httpcache/firsthand': 1,
 'httpcache/hit': 1,
 'httpcache/miss': 1,
 'httpcache/store': 1,
 'httpcompression/response_bytes': 612446,
 'httpcompression/response_count': 2,
 'item_dropped_count': 19,
 'item_dropped_reasons_count/DropItem': 19,
 'item_scraped_count': 1,
 'items_per_minute': None,
 'log_count/ERROR': 10,
 'log_count/INFO': 13,
 'log_count/WARNING': 23,
 'memusage/max': 75710464,
 'memusage/startup': 75710464,
 'request_depth_max': 1,
 'response_received_count': 2,
 'responses_per_minute': None,
 'scheduler/dequeued': 2,
 'scheduler/dequeued/memory': 2,
 'scheduler/enqueued': 2,
 'scheduler/enqueued/memory': 2,
 'start_time': datetime.datetime(2025, 6, 13, 13, 53, 20, 540765, tzinfo=datetime.timezone.utc)}
2025-06-13 15:53:21 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 15:54:50 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:54:50 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:54:50 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:54:50 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:54:50 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.feedexport.FeedExporter',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:54:50 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:54:50 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:54:50 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:54:50 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:54:50 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:54:50 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 15:54:50 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:54:50 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:54:50 [review] INFO: Spider opened: review
2025-06-13 15:54:50 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:54:50 [review] INFO: Product 118805 has 153 reviews across 8 pages
2025-06-13 15:54:51 [review] INFO: Parsing reviews page 1 for product 118805
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 11:50',
 'review_has_answer': False,
 'review_id': '38766955',
 'reviewer_id': 'daryanahmmed',
 'reviewer_name': 'Daryan Ahmed',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:54:51.143929',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 04:27',
 'review_has_answer': False,
 'review_id': '38826636',
 'reviewer_id': 'chalermpetchara',
 'reviewer_name': 'chalerm petcharat',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:54:51.147020',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '599 Pitt Petruschke 2025.06.11 09:21 # ich nutze den EA '
                   'jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das '
                   'bedeutet, der macht wenige aber dafür profitabele Trades, '
                   'was meinem Trading Stil entgegenkommt. Der Support '
                   'arbeitet super schnell...',
 'review_datetime': '2025.06.11 09:21',
 'review_has_answer': False,
 'review_id': '38655413',
 'reviewer_id': 'pablo1904',
 'reviewer_name': 'Pitt Petruschke',
 'reviewer_score': 599,
 'scraped_at': '2025-06-13T15:54:51.149450',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I decided to leave a review after 2-3 weeks of testing on '
                   'a live account. I really like the way QQ5 works. Deals are '
                   'opened not very often, but qualitatively and I understand '
                   'that this is due to the fact that EA waits for the perfect '
                   'moment, taking into account the market situation. And when '
                   'it opens a deal, then another one and another and '
                   'eventually closes almost all deals with a profit. I have '
                   'never had a series of trades that went into the minus, '
                   'always in the plus. My settings are automatic with medium '
                   'risk. My broker is ByBit. I am very happy with the result '
                   "of QQ5's work, I will continue to trust him to manage my "
                   'budget. Thank you for such a quality EA! I recommend '
                   'everyone to buy it, the price matches the quality - and it '
                   'will definitely pay off!',
 'review_datetime': '2025.06.11 08:19',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'kirilka59',
 'reviewer_name': 'Kirilka59',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:54:51.151846',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '48 goateeeth maker 2025.06.08 02:25 # ⭐⭐⭐⭐⭐ Excellent '
                   "EA—Profitable and Reliable! I've been using this EA on MT5 "
                   "for several weeks, and it's exceeded my expectations. The "
                   'performance has been consistent, generating steady profits '
                   'while effectively managing risks. Setup was '
                   'straightforward, and customer support has been responsive '
                   'whenever I had questions. Highly recommend this EA to '
                   'anyone looking for an automated, stress-free trading '
                   'solution!',
 'review_datetime': '2025.06.08 02:25',
 'review_has_answer': False,
 'review_id': '38333720',
 'reviewer_id': 'goateeeth',
 'reviewer_name': 'goateeeth maker',
 'reviewer_score': 48,
 'scraped_at': '2025-06-13T15:54:51.155281',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '🌟 An Expert Advisor That Truly Delivers! 🌟 I’ve been using '
                   'Quantum Queen MT5 for a short time, but I’m already seeing '
                   'my account grow steadily. A colleague of mine has been '
                   'using it for over two months, and his results have been '
                   'exceptional and consistent, without hesitation. 🔹 It’s one '
                   'of the very few algobots on MetaTrader 5 that actually '
                   'works. 🔹 The developer is very professional and always '
                   'quick to respond to any questions. 🔹 It was recommended to '
                   'me by someone extremely meticulous… and it has exceeded '
                   'all expectations. 💰 It’s worth every cent of the price. '
                   'This is a professional tool, not just another empty '
                   'promise. 🔒 Reliable, profitable, and well-supported. '
                   'Highly recommended!',
 'review_datetime': '2025.06.06 23:02',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'saman83',
 'reviewer_name': 'Saman83',
 'reviewer_score': 79,
 'scraped_at': '2025-06-13T15:54:51.157609',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '53 SAMUEL DE SOUZA 2025.06.06 22:12 # Absolutely '
                   'Phenomenal – A True Queen in the Game! I’ve tested over 40 '
                   'different AEs in my trading journey, and I can confidently '
                   'say the Quantum Queen MT5 is in a league of its own. It’s '
                   'not just another gold (XAUUSD) AE—it’s a '
                   'precision-engineered powerhouse. From setup to execution, '
                   'everything runs smoothly and efficiently, adapting '
                   'perfectly to changing market conditions. Alongside The '
                   'Emperor, Quantum Queen is easily one of the two best AEs '
                   'I’ve ever come across. The consistency, reliability, and '
                   'intelligent trade management this bot brings are '
                   'unmatched. Whether you’re a seasoned trader or just '
                   'starting out, this is a must-have for your arsenal. Highly '
                   'recommend to anyone serious about mastering gold on MT5!',
 'review_datetime': '2025.06.06 22:12',
 'review_has_answer': False,
 'review_id': '38737428',
 'reviewer_id': 'swtsam2',
 'reviewer_name': 'SAMUEL DE SOUZA',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:54:51.159785',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '购买这个EA之后我基于最近半年短期做了很多次回测 根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 '
                   '每一次都是盈利的 而且都是非常可观的 用我们国家的一句老话来说就是 兵欲善其事 必先利其器 '
                   '我觉得QQ对我这个新手来说十分友好 并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的',
 'review_datetime': '2025.06.06 20:33',
 'review_has_answer': False,
 'review_id': '38705502',
 'reviewer_id': 'noalc',
 'reviewer_name': 'Noalc',
 'reviewer_score': 22,
 'scraped_at': '2025-06-13T15:54:51.161860',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "Bonjour je viens d'acheter expert Quantum Queen MT5 c'est "
                   "un très bon expert il est très stable il n'y a des perte "
                   'que très minime et donne plus de bon profit je le trouve '
                   'excellent. Très bon service après vente pour '
                   "l'installation et les conseils de Bogdan Ion Puscasu il a "
                   'répondu à mes demandes et à mes messages avec une rapidité '
                   'exemplaire je suis très satisfait merci beaucoup.',
 'review_datetime': '2025.06.06 02:24',
 'review_has_answer': False,
 'review_id': '38679597',
 'reviewer_id': 'dnamron',
 'reviewer_name': 'Dnamron',
 'reviewer_score': 66,
 'scraped_at': '2025-06-13T15:54:51.164106',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '"This is an EA that allows you to trade with great peace '
                   'of mind." 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:43 # Thank you so much! That’s exactly what '
                   'I wanted to achieve with Quantum Queen — an EA that brings '
                   'not just results, but also peace of mind. Trading '
                   'shouldn’t be stressful, and I’m really glad you’re feeling '
                   'that sense of calm while using it. If you ever have '
                   'questions or need anything, feel free to reach out '
                   'anytime!',
 'review_datetime': '2025.06.04 08:33',
 'review_has_answer': False,
 'review_id': '38661377',
 'reviewer_id': 'jza80spl',
 'reviewer_name': 'jza80spl',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:54:51.169834',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '378 Guorong He 2025.06.03 16:06 # Very good grid system. '
                   'Thanks Bogdan！ 34750 Reply from developer Bogdan Ion '
                   'Puscasu 2025.06.05 14:42 # Thank you so much for your '
                   "review! I’m really glad to hear you're enjoying the grid "
                   'system. It’s designed to be both smart and steady, and I’m '
                   'happy it’s working well for you. Appreciate the kind words '
                   '– and if you ever need help or want to explore more '
                   'Quantum EAs, I’m just a message away!',
 'review_datetime': '2025.06.03 16:06',
 'review_has_answer': False,
 'review_id': '38691977',
 'reviewer_id': 'tokio147',
 'reviewer_name': 'Guorong He',
 'reviewer_score': 378,
 'scraped_at': '2025-06-13T15:54:51.172819',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.02 21:18',
 'review_has_answer': False,
 'review_id': '38596644',
 'reviewer_id': 'molobamllr',
 'reviewer_name': 'Lorenz Goeminne',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:54:51.176521',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bought the EA last week and already made about 4% in just '
                   'one week! Really happy with the performance so far. The '
                   'documentation provided is very detailed and informative — '
                   'it made setup and understanding the strategy very easy. '
                   'Looking forward to seeing more great results. 34750 Reply '
                   'from developer Bogdan Ion Puscasu 2025.06.05 14:37 # '
                   "That's fantastic to hear! I'm really glad the setup "
                   "process went smoothly and that you're already seeing solid "
                   'results in just one week — 4% is a great start! Thanks for '
                   'pointing out the documentation too, I put a lot of effort '
                   'into making it as clear and helpful as possible. Wishing '
                   'you continued success, and if you ever need anything, '
                   'don’t hesitate to reach out.',
 'review_datetime': '2025.06.02 13:36',
 'review_has_answer': False,
 'review_id': '38600643',
 'reviewer_id': 'nasoo',
 'reviewer_name': 'Nasoo',
 'reviewer_score': 785,
 'scraped_at': '2025-06-13T15:54:51.183248',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I am very satisfied with the results from the 1st week, '
                   'its the same as in the backtests. I fully recommend it. '
                   '34750 Reply from developer Bogdan Ion Puscasu 2025.06.05 '
                   "14:35 # Thank you so much for the great feedback! I'm "
                   'really glad to hear that your first week with Quantum '
                   "Queen matches the backtests — that's always a great sign. "
                   "It means a lot to know you're satisfied and confident in "
                   'the EA. Wishing you continued success and steady profits '
                   "ahead! If you ever need help or have questions, I'm here.",
 'review_datetime': '2025.06.01 15:52',
 'review_has_answer': False,
 'review_id': '38580848',
 'reviewer_id': 'francesc00',
 'reviewer_name': 'Francesc00',
 'reviewer_score': 34,
 'scraped_at': '2025-06-13T15:54:51.186052',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.05.31 06:14',
 'review_has_answer': False,
 'review_id': '38668358',
 'reviewer_id': 'schnitzler88',
 'reviewer_name': 'Extoflex',
 'reviewer_score': 328,
 'scraped_at': '2025-06-13T15:54:51.189596',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '40 Lucas Ramos 2025.05.30 09:43 # O Bogdan é um Trader '
                   'extremamente responsável e simpático e oferece suporte '
                   'rápido, a Quantum Queen é uma ótima EA que já me rendeu '
                   'bons lucros com baixos riscos, recomendo fortemente o uso '
                   'dessa EA para Traders responsáveis e que queiram fazer seu '
                   'dinheiro render sem muito esforço 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:33 # Thank you '
                   "so much for your kind words and trust, Lucas! 🙏 I'm really "
                   'happy to hear that Quantum Queen is delivering good '
                   'profits with low risk for you — exactly what I designed it '
                   'for. I truly appreciate your support and recommendation, '
                   "and I'm always here if you need help or have any "
                   'questions. Wishing you continued success and steady growth '
                   "— let's keep making the most out of every trade!",
 'review_datetime': '2025.05.30 09:43',
 'review_has_answer': False,
 'review_id': '38537336',
 'reviewer_id': 'lucasramoss',
 'reviewer_name': 'Lucas Ramos',
 'reviewer_score': 40,
 'scraped_at': '2025-06-13T15:54:51.192256',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bogdan has been incredibly responsive, often replying '
                   'within minutes, which makes it feel like I have a tech '
                   'team right beside me — something I really appreciate. I’ve '
                   'been running the EA from May 26th to May 29th, and it '
                   'delivered a 2% profit within just four days using the '
                   'default risk settings. It doesn’t use a martingale '
                   'strategy, but it does attempt to average price levels in a '
                   'controlled manner. I especially like that it only executes '
                   'trades when specific market conditions are met, aiming for '
                   'higher probability setups. So far, the performance has '
                   'been solid and feels safe. Highly recommended, and based '
                   'on these results, I plan to explore more of their '
                   'products. 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:31 # Thank you so much for the thoughtful '
                   'and detailed feedback, Leo! 🙏 I’m really glad to hear that '
                   'you appreciate the way Quantum Queen handles trades — '
                   'focused, careful, and without martingale risk. I’ve spent '
                   'a lot of time fine-tuning the strategy to be both smart '
                   'and safe, and it’s great to know it’s performing well for '
                   'you. I’m also always happy to help, so don’t hesitate to '
                   'reach out anytime. Looking forward to having you explore '
                   'the rest of the Quantum family — there’s more good stuff '
                   'waiting!',
 'review_datetime': '2025.05.30 08:56',
 'review_has_answer': False,
 'review_id': '38237707',
 'reviewer_id': 'chasuhun23',
 'reviewer_name': 'Leo',
 'reviewer_score': 56,
 'scraped_at': '2025-06-13T15:54:51.197681',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '24 Michael Schnitzer 2025.05.29 17:57 # I can highly '
                   'recommend the Queen. You get an immediate response to any '
                   'kind of question, and the system truly works. Thank you, '
                   'Bogdan! 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:29 # Thank you so much for the wonderful '
                   'feedback! I’m really happy to hear that Quantum Queen is '
                   'working well for you and that you feel supported — I’m '
                   'always here to help whenever you need. Wishing you '
                   'continued success and steady profits!',
 'review_datetime': '2025.05.29 17:57',
 'review_has_answer': False,
 'review_id': '38508546',
 'reviewer_id': 'm_schnitzer',
 'reviewer_name': 'Michael Schnitzer',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:54:51.200890',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '35 Steels is 2025.05.29 12:33 # "It is a great system and '
                   'a profitable, thank you Bogdan! 34750 Reply from developer '
                   'Bogdan Ion Puscasu 2025.06.05 14:28 # Thank you so much '
                   'for your kind words! I’m really glad to hear you’re seeing '
                   'great results with the system. That’s exactly what I aimed '
                   'for when building Quantum Queen. Your support means a lot '
                   '— thank you!',
 'review_datetime': '2025.05.29 12:33',
 'review_has_answer': False,
 'review_id': '38517999',
 'reviewer_id': 'steelis',
 'reviewer_name': 'Steels is',
 'reviewer_score': 35,
 'scraped_at': '2025-06-13T15:54:51.203684',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:51 [review] INFO: Parsing reviews page 8 for product 118805
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Backtest results correlate very well with live '
                   'performance. Owner is super responsive and the Quantum '
                   'community is supportive and helpful as well. This has been '
                   'an excellent experience. Bought it on late May and profit '
                   'has already covered the cost of this EA (including VPS '
                   'cost). If you manage your risk and be patient, it just '
                   'pays itself off. This is as close you can get to an golden '
                   'goose.',
 'review_datetime': '2025.06.12 15:58',
 'review_has_answer': False,
 'review_id': '38658708',
 'reviewer_id': 'tiltedk',
 'reviewer_name': 'Thanos',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:54:52.009681',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 11:50',
 'review_has_answer': False,
 'review_id': '38766955',
 'reviewer_id': 'daryanahmmed',
 'reviewer_name': 'Daryan Ahmed',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:54:52.013233',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 04:27',
 'review_has_answer': False,
 'review_id': '38826636',
 'reviewer_id': 'chalermpetchara',
 'reviewer_name': 'chalerm petcharat',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:54:52.016280',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '599 Pitt Petruschke 2025.06.11 09:21 # ich nutze den EA '
                   'jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das '
                   'bedeutet, der macht wenige aber dafür profitabele Trades, '
                   'was meinem Trading Stil entgegenkommt. Der Support '
                   'arbeitet super schnell...',
 'review_datetime': '2025.06.11 09:21',
 'review_has_answer': False,
 'review_id': '38655413',
 'reviewer_id': 'pablo1904',
 'reviewer_name': 'Pitt Petruschke',
 'reviewer_score': 599,
 'scraped_at': '2025-06-13T15:54:52.018840',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I decided to leave a review after 2-3 weeks of testing on '
                   'a live account. I really like the way QQ5 works. Deals are '
                   'opened not very often, but qualitatively and I understand '
                   'that this is due to the fact that EA waits for the perfect '
                   'moment, taking into account the market situation. And when '
                   'it opens a deal, then another one and another and '
                   'eventually closes almost all deals with a profit. I have '
                   'never had a series of trades that went into the minus, '
                   'always in the plus. My settings are automatic with medium '
                   'risk. My broker is ByBit. I am very happy with the result '
                   "of QQ5's work, I will continue to trust him to manage my "
                   'budget. Thank you for such a quality EA! I recommend '
                   'everyone to buy it, the price matches the quality - and it '
                   'will definitely pay off!',
 'review_datetime': '2025.06.11 08:19',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'kirilka59',
 'reviewer_name': 'Kirilka59',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:54:52.021209',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '48 goateeeth maker 2025.06.08 02:25 # ⭐⭐⭐⭐⭐ Excellent '
                   "EA—Profitable and Reliable! I've been using this EA on MT5 "
                   "for several weeks, and it's exceeded my expectations. The "
                   'performance has been consistent, generating steady profits '
                   'while effectively managing risks. Setup was '
                   'straightforward, and customer support has been responsive '
                   'whenever I had questions. Highly recommend this EA to '
                   'anyone looking for an automated, stress-free trading '
                   'solution!',
 'review_datetime': '2025.06.08 02:25',
 'review_has_answer': False,
 'review_id': '38333720',
 'reviewer_id': 'goateeeth',
 'reviewer_name': 'goateeeth maker',
 'reviewer_score': 48,
 'scraped_at': '2025-06-13T15:54:52.025022',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '🌟 An Expert Advisor That Truly Delivers! 🌟 I’ve been using '
                   'Quantum Queen MT5 for a short time, but I’m already seeing '
                   'my account grow steadily. A colleague of mine has been '
                   'using it for over two months, and his results have been '
                   'exceptional and consistent, without hesitation. 🔹 It’s one '
                   'of the very few algobots on MetaTrader 5 that actually '
                   'works. 🔹 The developer is very professional and always '
                   'quick to respond to any questions. 🔹 It was recommended to '
                   'me by someone extremely meticulous… and it has exceeded '
                   'all expectations. 💰 It’s worth every cent of the price. '
                   'This is a professional tool, not just another empty '
                   'promise. 🔒 Reliable, profitable, and well-supported. '
                   'Highly recommended!',
 'review_datetime': '2025.06.06 23:02',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'saman83',
 'reviewer_name': 'Saman83',
 'reviewer_score': 79,
 'scraped_at': '2025-06-13T15:54:52.027206',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '53 SAMUEL DE SOUZA 2025.06.06 22:12 # Absolutely '
                   'Phenomenal – A True Queen in the Game! I’ve tested over 40 '
                   'different AEs in my trading journey, and I can confidently '
                   'say the Quantum Queen MT5 is in a league of its own. It’s '
                   'not just another gold (XAUUSD) AE—it’s a '
                   'precision-engineered powerhouse. From setup to execution, '
                   'everything runs smoothly and efficiently, adapting '
                   'perfectly to changing market conditions. Alongside The '
                   'Emperor, Quantum Queen is easily one of the two best AEs '
                   'I’ve ever come across. The consistency, reliability, and '
                   'intelligent trade management this bot brings are '
                   'unmatched. Whether you’re a seasoned trader or just '
                   'starting out, this is a must-have for your arsenal. Highly '
                   'recommend to anyone serious about mastering gold on MT5!',
 'review_datetime': '2025.06.06 22:12',
 'review_has_answer': False,
 'review_id': '38737428',
 'reviewer_id': 'swtsam2',
 'reviewer_name': 'SAMUEL DE SOUZA',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:54:52.029567',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '购买这个EA之后我基于最近半年短期做了很多次回测 根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 '
                   '每一次都是盈利的 而且都是非常可观的 用我们国家的一句老话来说就是 兵欲善其事 必先利其器 '
                   '我觉得QQ对我这个新手来说十分友好 并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的',
 'review_datetime': '2025.06.06 20:33',
 'review_has_answer': False,
 'review_id': '38705502',
 'reviewer_id': 'noalc',
 'reviewer_name': 'Noalc',
 'reviewer_score': 22,
 'scraped_at': '2025-06-13T15:54:52.031743',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "Bonjour je viens d'acheter expert Quantum Queen MT5 c'est "
                   "un très bon expert il est très stable il n'y a des perte "
                   'que très minime et donne plus de bon profit je le trouve '
                   'excellent. Très bon service après vente pour '
                   "l'installation et les conseils de Bogdan Ion Puscasu il a "
                   'répondu à mes demandes et à mes messages avec une rapidité '
                   'exemplaire je suis très satisfait merci beaucoup.',
 'review_datetime': '2025.06.06 02:24',
 'review_has_answer': False,
 'review_id': '38679597',
 'reviewer_id': 'dnamron',
 'reviewer_name': 'Dnamron',
 'reviewer_score': 66,
 'scraped_at': '2025-06-13T15:54:52.033882',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '"This is an EA that allows you to trade with great peace '
                   'of mind." 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:43 # Thank you so much! That’s exactly what '
                   'I wanted to achieve with Quantum Queen — an EA that brings '
                   'not just results, but also peace of mind. Trading '
                   'shouldn’t be stressful, and I’m really glad you’re feeling '
                   'that sense of calm while using it. If you ever have '
                   'questions or need anything, feel free to reach out '
                   'anytime!',
 'review_datetime': '2025.06.04 08:33',
 'review_has_answer': False,
 'review_id': '38661377',
 'reviewer_id': 'jza80spl',
 'reviewer_name': 'jza80spl',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:54:52.039516',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '378 Guorong He 2025.06.03 16:06 # Very good grid system. '
                   'Thanks Bogdan！ 34750 Reply from developer Bogdan Ion '
                   'Puscasu 2025.06.05 14:42 # Thank you so much for your '
                   "review! I’m really glad to hear you're enjoying the grid "
                   'system. It’s designed to be both smart and steady, and I’m '
                   'happy it’s working well for you. Appreciate the kind words '
                   '– and if you ever need help or want to explore more '
                   'Quantum EAs, I’m just a message away!',
 'review_datetime': '2025.06.03 16:06',
 'review_has_answer': False,
 'review_id': '38691977',
 'reviewer_id': 'tokio147',
 'reviewer_name': 'Guorong He',
 'reviewer_score': 378,
 'scraped_at': '2025-06-13T15:54:52.043028',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.02 21:18',
 'review_has_answer': False,
 'review_id': '38596644',
 'reviewer_id': 'molobamllr',
 'reviewer_name': 'Lorenz Goeminne',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:54:52.046977',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bought the EA last week and already made about 4% in just '
                   'one week! Really happy with the performance so far. The '
                   'documentation provided is very detailed and informative — '
                   'it made setup and understanding the strategy very easy. '
                   'Looking forward to seeing more great results. 34750 Reply '
                   'from developer Bogdan Ion Puscasu 2025.06.05 14:37 # '
                   "That's fantastic to hear! I'm really glad the setup "
                   "process went smoothly and that you're already seeing solid "
                   'results in just one week — 4% is a great start! Thanks for '
                   'pointing out the documentation too, I put a lot of effort '
                   'into making it as clear and helpful as possible. Wishing '
                   'you continued success, and if you ever need anything, '
                   'don’t hesitate to reach out.',
 'review_datetime': '2025.06.02 13:36',
 'review_has_answer': False,
 'review_id': '38600643',
 'reviewer_id': 'nasoo',
 'reviewer_name': 'Nasoo',
 'reviewer_score': 785,
 'scraped_at': '2025-06-13T15:54:52.057044',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I am very satisfied with the results from the 1st week, '
                   'its the same as in the backtests. I fully recommend it. '
                   '34750 Reply from developer Bogdan Ion Puscasu 2025.06.05 '
                   "14:35 # Thank you so much for the great feedback! I'm "
                   'really glad to hear that your first week with Quantum '
                   "Queen matches the backtests — that's always a great sign. "
                   "It means a lot to know you're satisfied and confident in "
                   'the EA. Wishing you continued success and steady profits '
                   "ahead! If you ever need help or have questions, I'm here.",
 'review_datetime': '2025.06.01 15:52',
 'review_has_answer': False,
 'review_id': '38580848',
 'reviewer_id': 'francesc00',
 'reviewer_name': 'Francesc00',
 'reviewer_score': 34,
 'scraped_at': '2025-06-13T15:54:52.060722',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.05.31 06:14',
 'review_has_answer': False,
 'review_id': '38668358',
 'reviewer_id': 'schnitzler88',
 'reviewer_name': 'Extoflex',
 'reviewer_score': 328,
 'scraped_at': '2025-06-13T15:54:52.064588',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '40 Lucas Ramos 2025.05.30 09:43 # O Bogdan é um Trader '
                   'extremamente responsável e simpático e oferece suporte '
                   'rápido, a Quantum Queen é uma ótima EA que já me rendeu '
                   'bons lucros com baixos riscos, recomendo fortemente o uso '
                   'dessa EA para Traders responsáveis e que queiram fazer seu '
                   'dinheiro render sem muito esforço 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:33 # Thank you '
                   "so much for your kind words and trust, Lucas! 🙏 I'm really "
                   'happy to hear that Quantum Queen is delivering good '
                   'profits with low risk for you — exactly what I designed it '
                   'for. I truly appreciate your support and recommendation, '
                   "and I'm always here if you need help or have any "
                   'questions. Wishing you continued success and steady growth '
                   "— let's keep making the most out of every trade!",
 'review_datetime': '2025.05.30 09:43',
 'review_has_answer': False,
 'review_id': '38537336',
 'reviewer_id': 'lucasramoss',
 'reviewer_name': 'Lucas Ramos',
 'reviewer_score': 40,
 'scraped_at': '2025-06-13T15:54:52.072314',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bogdan has been incredibly responsive, often replying '
                   'within minutes, which makes it feel like I have a tech '
                   'team right beside me — something I really appreciate. I’ve '
                   'been running the EA from May 26th to May 29th, and it '
                   'delivered a 2% profit within just four days using the '
                   'default risk settings. It doesn’t use a martingale '
                   'strategy, but it does attempt to average price levels in a '
                   'controlled manner. I especially like that it only executes '
                   'trades when specific market conditions are met, aiming for '
                   'higher probability setups. So far, the performance has '
                   'been solid and feels safe. Highly recommended, and based '
                   'on these results, I plan to explore more of their '
                   'products. 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:31 # Thank you so much for the thoughtful '
                   'and detailed feedback, Leo! 🙏 I’m really glad to hear that '
                   'you appreciate the way Quantum Queen handles trades — '
                   'focused, careful, and without martingale risk. I’ve spent '
                   'a lot of time fine-tuning the strategy to be both smart '
                   'and safe, and it’s great to know it’s performing well for '
                   'you. I’m also always happy to help, so don’t hesitate to '
                   'reach out anytime. Looking forward to having you explore '
                   'the rest of the Quantum family — there’s more good stuff '
                   'waiting!',
 'review_datetime': '2025.05.30 08:56',
 'review_has_answer': False,
 'review_id': '38237707',
 'reviewer_id': 'chasuhun23',
 'reviewer_name': 'Leo',
 'reviewer_score': 56,
 'scraped_at': '2025-06-13T15:54:52.078689',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '24 Michael Schnitzer 2025.05.29 17:57 # I can highly '
                   'recommend the Queen. You get an immediate response to any '
                   'kind of question, and the system truly works. Thank you, '
                   'Bogdan! 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:29 # Thank you so much for the wonderful '
                   'feedback! I’m really happy to hear that Quantum Queen is '
                   'working well for you and that you feel supported — I’m '
                   'always here to help whenever you need. Wishing you '
                   'continued success and steady profits!',
 'review_datetime': '2025.05.29 17:57',
 'review_has_answer': False,
 'review_id': '38508546',
 'reviewer_id': 'm_schnitzer',
 'reviewer_name': 'Michael Schnitzer',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:54:52.087523',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '35 Steels is 2025.05.29 12:33 # "It is a great system and '
                   'a profitable, thank you Bogdan! 34750 Reply from developer '
                   'Bogdan Ion Puscasu 2025.06.05 14:28 # Thank you so much '
                   'for your kind words! I’m really glad to hear you’re seeing '
                   'great results with the system. That’s exactly what I aimed '
                   'for when building Quantum Queen. Your support means a lot '
                   '— thank you!',
 'review_datetime': '2025.05.29 12:33',
 'review_has_answer': False,
 'review_id': '38517999',
 'reviewer_id': 'steelis',
 'reviewer_name': 'Steels is',
 'reviewer_score': 35,
 'scraped_at': '2025-06-13T15:54:52.093533',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:52 [review] INFO: Parsing reviews page 7 for product 118805
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Backtest results correlate very well with live '
                   'performance. Owner is super responsive and the Quantum '
                   'community is supportive and helpful as well. This has been '
                   'an excellent experience. Bought it on late May and profit '
                   'has already covered the cost of this EA (including VPS '
                   'cost). If you manage your risk and be patient, it just '
                   'pays itself off. This is as close you can get to an golden '
                   'goose.',
 'review_datetime': '2025.06.12 15:58',
 'review_has_answer': False,
 'review_id': '38658708',
 'reviewer_id': 'tiltedk',
 'reviewer_name': 'Thanos',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:54:52.991968',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 11:50',
 'review_has_answer': False,
 'review_id': '38766955',
 'reviewer_id': 'daryanahmmed',
 'reviewer_name': 'Daryan Ahmed',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:54:52.994365',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 04:27',
 'review_has_answer': False,
 'review_id': '38826636',
 'reviewer_id': 'chalermpetchara',
 'reviewer_name': 'chalerm petcharat',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:54:52.996378',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '599 Pitt Petruschke 2025.06.11 09:21 # ich nutze den EA '
                   'jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das '
                   'bedeutet, der macht wenige aber dafür profitabele Trades, '
                   'was meinem Trading Stil entgegenkommt. Der Support '
                   'arbeitet super schnell...',
 'review_datetime': '2025.06.11 09:21',
 'review_has_answer': False,
 'review_id': '38655413',
 'reviewer_id': 'pablo1904',
 'reviewer_name': 'Pitt Petruschke',
 'reviewer_score': 599,
 'scraped_at': '2025-06-13T15:54:52.998149',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I decided to leave a review after 2-3 weeks of testing on '
                   'a live account. I really like the way QQ5 works. Deals are '
                   'opened not very often, but qualitatively and I understand '
                   'that this is due to the fact that EA waits for the perfect '
                   'moment, taking into account the market situation. And when '
                   'it opens a deal, then another one and another and '
                   'eventually closes almost all deals with a profit. I have '
                   'never had a series of trades that went into the minus, '
                   'always in the plus. My settings are automatic with medium '
                   'risk. My broker is ByBit. I am very happy with the result '
                   "of QQ5's work, I will continue to trust him to manage my "
                   'budget. Thank you for such a quality EA! I recommend '
                   'everyone to buy it, the price matches the quality - and it '
                   'will definitely pay off!',
 'review_datetime': '2025.06.11 08:19',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'kirilka59',
 'reviewer_name': 'Kirilka59',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:54:52.999941',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '48 goateeeth maker 2025.06.08 02:25 # ⭐⭐⭐⭐⭐ Excellent '
                   "EA—Profitable and Reliable! I've been using this EA on MT5 "
                   "for several weeks, and it's exceeded my expectations. The "
                   'performance has been consistent, generating steady profits '
                   'while effectively managing risks. Setup was '
                   'straightforward, and customer support has been responsive '
                   'whenever I had questions. Highly recommend this EA to '
                   'anyone looking for an automated, stress-free trading '
                   'solution!',
 'review_datetime': '2025.06.08 02:25',
 'review_has_answer': False,
 'review_id': '38333720',
 'reviewer_id': 'goateeeth',
 'reviewer_name': 'goateeeth maker',
 'reviewer_score': 48,
 'scraped_at': '2025-06-13T15:54:53.001631',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '🌟 An Expert Advisor That Truly Delivers! 🌟 I’ve been using '
                   'Quantum Queen MT5 for a short time, but I’m already seeing '
                   'my account grow steadily. A colleague of mine has been '
                   'using it for over two months, and his results have been '
                   'exceptional and consistent, without hesitation. 🔹 It’s one '
                   'of the very few algobots on MetaTrader 5 that actually '
                   'works. 🔹 The developer is very professional and always '
                   'quick to respond to any questions. 🔹 It was recommended to '
                   'me by someone extremely meticulous… and it has exceeded '
                   'all expectations. 💰 It’s worth every cent of the price. '
                   'This is a professional tool, not just another empty '
                   'promise. 🔒 Reliable, profitable, and well-supported. '
                   'Highly recommended!',
 'review_datetime': '2025.06.06 23:02',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'saman83',
 'reviewer_name': 'Saman83',
 'reviewer_score': 79,
 'scraped_at': '2025-06-13T15:54:53.003255',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '53 SAMUEL DE SOUZA 2025.06.06 22:12 # Absolutely '
                   'Phenomenal – A True Queen in the Game! I’ve tested over 40 '
                   'different AEs in my trading journey, and I can confidently '
                   'say the Quantum Queen MT5 is in a league of its own. It’s '
                   'not just another gold (XAUUSD) AE—it’s a '
                   'precision-engineered powerhouse. From setup to execution, '
                   'everything runs smoothly and efficiently, adapting '
                   'perfectly to changing market conditions. Alongside The '
                   'Emperor, Quantum Queen is easily one of the two best AEs '
                   'I’ve ever come across. The consistency, reliability, and '
                   'intelligent trade management this bot brings are '
                   'unmatched. Whether you’re a seasoned trader or just '
                   'starting out, this is a must-have for your arsenal. Highly '
                   'recommend to anyone serious about mastering gold on MT5!',
 'review_datetime': '2025.06.06 22:12',
 'review_has_answer': False,
 'review_id': '38737428',
 'reviewer_id': 'swtsam2',
 'reviewer_name': 'SAMUEL DE SOUZA',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:54:53.006189',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '购买这个EA之后我基于最近半年短期做了很多次回测 根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 '
                   '每一次都是盈利的 而且都是非常可观的 用我们国家的一句老话来说就是 兵欲善其事 必先利其器 '
                   '我觉得QQ对我这个新手来说十分友好 并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的',
 'review_datetime': '2025.06.06 20:33',
 'review_has_answer': False,
 'review_id': '38705502',
 'reviewer_id': 'noalc',
 'reviewer_name': 'Noalc',
 'reviewer_score': 22,
 'scraped_at': '2025-06-13T15:54:53.007874',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "Bonjour je viens d'acheter expert Quantum Queen MT5 c'est "
                   "un très bon expert il est très stable il n'y a des perte "
                   'que très minime et donne plus de bon profit je le trouve '
                   'excellent. Très bon service après vente pour '
                   "l'installation et les conseils de Bogdan Ion Puscasu il a "
                   'répondu à mes demandes et à mes messages avec une rapidité '
                   'exemplaire je suis très satisfait merci beaucoup.',
 'review_datetime': '2025.06.06 02:24',
 'review_has_answer': False,
 'review_id': '38679597',
 'reviewer_id': 'dnamron',
 'reviewer_name': 'Dnamron',
 'reviewer_score': 66,
 'scraped_at': '2025-06-13T15:54:53.009458',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '"This is an EA that allows you to trade with great peace '
                   'of mind." 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:43 # Thank you so much! That’s exactly what '
                   'I wanted to achieve with Quantum Queen — an EA that brings '
                   'not just results, but also peace of mind. Trading '
                   'shouldn’t be stressful, and I’m really glad you’re feeling '
                   'that sense of calm while using it. If you ever have '
                   'questions or need anything, feel free to reach out '
                   'anytime!',
 'review_datetime': '2025.06.04 08:33',
 'review_has_answer': False,
 'review_id': '38661377',
 'reviewer_id': 'jza80spl',
 'reviewer_name': 'jza80spl',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:54:53.011797',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '378 Guorong He 2025.06.03 16:06 # Very good grid system. '
                   'Thanks Bogdan！ 34750 Reply from developer Bogdan Ion '
                   'Puscasu 2025.06.05 14:42 # Thank you so much for your '
                   "review! I’m really glad to hear you're enjoying the grid "
                   'system. It’s designed to be both smart and steady, and I’m '
                   'happy it’s working well for you. Appreciate the kind words '
                   '– and if you ever need help or want to explore more '
                   'Quantum EAs, I’m just a message away!',
 'review_datetime': '2025.06.03 16:06',
 'review_has_answer': False,
 'review_id': '38691977',
 'reviewer_id': 'tokio147',
 'reviewer_name': 'Guorong He',
 'reviewer_score': 378,
 'scraped_at': '2025-06-13T15:54:53.014645',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.02 21:18',
 'review_has_answer': False,
 'review_id': '38596644',
 'reviewer_id': 'molobamllr',
 'reviewer_name': 'Lorenz Goeminne',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:54:53.022187',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bought the EA last week and already made about 4% in just '
                   'one week! Really happy with the performance so far. The '
                   'documentation provided is very detailed and informative — '
                   'it made setup and understanding the strategy very easy. '
                   'Looking forward to seeing more great results. 34750 Reply '
                   'from developer Bogdan Ion Puscasu 2025.06.05 14:37 # '
                   "That's fantastic to hear! I'm really glad the setup "
                   "process went smoothly and that you're already seeing solid "
                   'results in just one week — 4% is a great start! Thanks for '
                   'pointing out the documentation too, I put a lot of effort '
                   'into making it as clear and helpful as possible. Wishing '
                   'you continued success, and if you ever need anything, '
                   'don’t hesitate to reach out.',
 'review_datetime': '2025.06.02 13:36',
 'review_has_answer': False,
 'review_id': '38600643',
 'reviewer_id': 'nasoo',
 'reviewer_name': 'Nasoo',
 'reviewer_score': 785,
 'scraped_at': '2025-06-13T15:54:53.025599',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I am very satisfied with the results from the 1st week, '
                   'its the same as in the backtests. I fully recommend it. '
                   '34750 Reply from developer Bogdan Ion Puscasu 2025.06.05 '
                   "14:35 # Thank you so much for the great feedback! I'm "
                   'really glad to hear that your first week with Quantum '
                   "Queen matches the backtests — that's always a great sign. "
                   "It means a lot to know you're satisfied and confident in "
                   'the EA. Wishing you continued success and steady profits '
                   "ahead! If you ever need help or have questions, I'm here.",
 'review_datetime': '2025.06.01 15:52',
 'review_has_answer': False,
 'review_id': '38580848',
 'reviewer_id': 'francesc00',
 'reviewer_name': 'Francesc00',
 'reviewer_score': 34,
 'scraped_at': '2025-06-13T15:54:53.028189',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.05.31 06:14',
 'review_has_answer': False,
 'review_id': '38668358',
 'reviewer_id': 'schnitzler88',
 'reviewer_name': 'Extoflex',
 'reviewer_score': 328,
 'scraped_at': '2025-06-13T15:54:53.031611',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '40 Lucas Ramos 2025.05.30 09:43 # O Bogdan é um Trader '
                   'extremamente responsável e simpático e oferece suporte '
                   'rápido, a Quantum Queen é uma ótima EA que já me rendeu '
                   'bons lucros com baixos riscos, recomendo fortemente o uso '
                   'dessa EA para Traders responsáveis e que queiram fazer seu '
                   'dinheiro render sem muito esforço 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:33 # Thank you '
                   "so much for your kind words and trust, Lucas! 🙏 I'm really "
                   'happy to hear that Quantum Queen is delivering good '
                   'profits with low risk for you — exactly what I designed it '
                   'for. I truly appreciate your support and recommendation, '
                   "and I'm always here if you need help or have any "
                   'questions. Wishing you continued success and steady growth '
                   "— let's keep making the most out of every trade!",
 'review_datetime': '2025.05.30 09:43',
 'review_has_answer': False,
 'review_id': '38537336',
 'reviewer_id': 'lucasramoss',
 'reviewer_name': 'Lucas Ramos',
 'reviewer_score': 40,
 'scraped_at': '2025-06-13T15:54:53.037972',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bogdan has been incredibly responsive, often replying '
                   'within minutes, which makes it feel like I have a tech '
                   'team right beside me — something I really appreciate. I’ve '
                   'been running the EA from May 26th to May 29th, and it '
                   'delivered a 2% profit within just four days using the '
                   'default risk settings. It doesn’t use a martingale '
                   'strategy, but it does attempt to average price levels in a '
                   'controlled manner. I especially like that it only executes '
                   'trades when specific market conditions are met, aiming for '
                   'higher probability setups. So far, the performance has '
                   'been solid and feels safe. Highly recommended, and based '
                   'on these results, I plan to explore more of their '
                   'products. 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:31 # Thank you so much for the thoughtful '
                   'and detailed feedback, Leo! 🙏 I’m really glad to hear that '
                   'you appreciate the way Quantum Queen handles trades — '
                   'focused, careful, and without martingale risk. I’ve spent '
                   'a lot of time fine-tuning the strategy to be both smart '
                   'and safe, and it’s great to know it’s performing well for '
                   'you. I’m also always happy to help, so don’t hesitate to '
                   'reach out anytime. Looking forward to having you explore '
                   'the rest of the Quantum family — there’s more good stuff '
                   'waiting!',
 'review_datetime': '2025.05.30 08:56',
 'review_has_answer': False,
 'review_id': '38237707',
 'reviewer_id': 'chasuhun23',
 'reviewer_name': 'Leo',
 'reviewer_score': 56,
 'scraped_at': '2025-06-13T15:54:53.040680',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '24 Michael Schnitzer 2025.05.29 17:57 # I can highly '
                   'recommend the Queen. You get an immediate response to any '
                   'kind of question, and the system truly works. Thank you, '
                   'Bogdan! 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:29 # Thank you so much for the wonderful '
                   'feedback! I’m really happy to hear that Quantum Queen is '
                   'working well for you and that you feel supported — I’m '
                   'always here to help whenever you need. Wishing you '
                   'continued success and steady profits!',
 'review_datetime': '2025.05.29 17:57',
 'review_has_answer': False,
 'review_id': '38508546',
 'reviewer_id': 'm_schnitzer',
 'reviewer_name': 'Michael Schnitzer',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:54:53.043466',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '35 Steels is 2025.05.29 12:33 # "It is a great system and '
                   'a profitable, thank you Bogdan! 34750 Reply from developer '
                   'Bogdan Ion Puscasu 2025.06.05 14:28 # Thank you so much '
                   'for your kind words! I’m really glad to hear you’re seeing '
                   'great results with the system. That’s exactly what I aimed '
                   'for when building Quantum Queen. Your support means a lot '
                   '— thank you!',
 'review_datetime': '2025.05.29 12:33',
 'review_has_answer': False,
 'review_id': '38517999',
 'reviewer_id': 'steelis',
 'reviewer_name': 'Steels is',
 'reviewer_score': 35,
 'scraped_at': '2025-06-13T15:54:53.046233',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:53 [review] INFO: Parsing reviews page 6 for product 118805
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Backtest results correlate very well with live '
                   'performance. Owner is super responsive and the Quantum '
                   'community is supportive and helpful as well. This has been '
                   'an excellent experience. Bought it on late May and profit '
                   'has already covered the cost of this EA (including VPS '
                   'cost). If you manage your risk and be patient, it just '
                   'pays itself off. This is as close you can get to an golden '
                   'goose.',
 'review_datetime': '2025.06.12 15:58',
 'review_has_answer': False,
 'review_id': '38658708',
 'reviewer_id': 'tiltedk',
 'reviewer_name': 'Thanos',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:54:54.005964',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 11:50',
 'review_has_answer': False,
 'review_id': '38766955',
 'reviewer_id': 'daryanahmmed',
 'reviewer_name': 'Daryan Ahmed',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:54:54.009263',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 04:27',
 'review_has_answer': False,
 'review_id': '38826636',
 'reviewer_id': 'chalermpetchara',
 'reviewer_name': 'chalerm petcharat',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:54:54.012880',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '599 Pitt Petruschke 2025.06.11 09:21 # ich nutze den EA '
                   'jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das '
                   'bedeutet, der macht wenige aber dafür profitabele Trades, '
                   'was meinem Trading Stil entgegenkommt. Der Support '
                   'arbeitet super schnell...',
 'review_datetime': '2025.06.11 09:21',
 'review_has_answer': False,
 'review_id': '38655413',
 'reviewer_id': 'pablo1904',
 'reviewer_name': 'Pitt Petruschke',
 'reviewer_score': 599,
 'scraped_at': '2025-06-13T15:54:54.015341',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I decided to leave a review after 2-3 weeks of testing on '
                   'a live account. I really like the way QQ5 works. Deals are '
                   'opened not very often, but qualitatively and I understand '
                   'that this is due to the fact that EA waits for the perfect '
                   'moment, taking into account the market situation. And when '
                   'it opens a deal, then another one and another and '
                   'eventually closes almost all deals with a profit. I have '
                   'never had a series of trades that went into the minus, '
                   'always in the plus. My settings are automatic with medium '
                   'risk. My broker is ByBit. I am very happy with the result '
                   "of QQ5's work, I will continue to trust him to manage my "
                   'budget. Thank you for such a quality EA! I recommend '
                   'everyone to buy it, the price matches the quality - and it '
                   'will definitely pay off!',
 'review_datetime': '2025.06.11 08:19',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'kirilka59',
 'reviewer_name': 'Kirilka59',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:54:54.018553',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '48 goateeeth maker 2025.06.08 02:25 # ⭐⭐⭐⭐⭐ Excellent '
                   "EA—Profitable and Reliable! I've been using this EA on MT5 "
                   "for several weeks, and it's exceeded my expectations. The "
                   'performance has been consistent, generating steady profits '
                   'while effectively managing risks. Setup was '
                   'straightforward, and customer support has been responsive '
                   'whenever I had questions. Highly recommend this EA to '
                   'anyone looking for an automated, stress-free trading '
                   'solution!',
 'review_datetime': '2025.06.08 02:25',
 'review_has_answer': False,
 'review_id': '38333720',
 'reviewer_id': 'goateeeth',
 'reviewer_name': 'goateeeth maker',
 'reviewer_score': 48,
 'scraped_at': '2025-06-13T15:54:54.027624',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '🌟 An Expert Advisor That Truly Delivers! 🌟 I’ve been using '
                   'Quantum Queen MT5 for a short time, but I’m already seeing '
                   'my account grow steadily. A colleague of mine has been '
                   'using it for over two months, and his results have been '
                   'exceptional and consistent, without hesitation. 🔹 It’s one '
                   'of the very few algobots on MetaTrader 5 that actually '
                   'works. 🔹 The developer is very professional and always '
                   'quick to respond to any questions. 🔹 It was recommended to '
                   'me by someone extremely meticulous… and it has exceeded '
                   'all expectations. 💰 It’s worth every cent of the price. '
                   'This is a professional tool, not just another empty '
                   'promise. 🔒 Reliable, profitable, and well-supported. '
                   'Highly recommended!',
 'review_datetime': '2025.06.06 23:02',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'saman83',
 'reviewer_name': 'Saman83',
 'reviewer_score': 79,
 'scraped_at': '2025-06-13T15:54:54.031038',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '53 SAMUEL DE SOUZA 2025.06.06 22:12 # Absolutely '
                   'Phenomenal – A True Queen in the Game! I’ve tested over 40 '
                   'different AEs in my trading journey, and I can confidently '
                   'say the Quantum Queen MT5 is in a league of its own. It’s '
                   'not just another gold (XAUUSD) AE—it’s a '
                   'precision-engineered powerhouse. From setup to execution, '
                   'everything runs smoothly and efficiently, adapting '
                   'perfectly to changing market conditions. Alongside The '
                   'Emperor, Quantum Queen is easily one of the two best AEs '
                   'I’ve ever come across. The consistency, reliability, and '
                   'intelligent trade management this bot brings are '
                   'unmatched. Whether you’re a seasoned trader or just '
                   'starting out, this is a must-have for your arsenal. Highly '
                   'recommend to anyone serious about mastering gold on MT5!',
 'review_datetime': '2025.06.06 22:12',
 'review_has_answer': False,
 'review_id': '38737428',
 'reviewer_id': 'swtsam2',
 'reviewer_name': 'SAMUEL DE SOUZA',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:54:54.038137',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '购买这个EA之后我基于最近半年短期做了很多次回测 根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 '
                   '每一次都是盈利的 而且都是非常可观的 用我们国家的一句老话来说就是 兵欲善其事 必先利其器 '
                   '我觉得QQ对我这个新手来说十分友好 并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的',
 'review_datetime': '2025.06.06 20:33',
 'review_has_answer': False,
 'review_id': '38705502',
 'reviewer_id': 'noalc',
 'reviewer_name': 'Noalc',
 'reviewer_score': 22,
 'scraped_at': '2025-06-13T15:54:54.041956',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "Bonjour je viens d'acheter expert Quantum Queen MT5 c'est "
                   "un très bon expert il est très stable il n'y a des perte "
                   'que très minime et donne plus de bon profit je le trouve '
                   'excellent. Très bon service après vente pour '
                   "l'installation et les conseils de Bogdan Ion Puscasu il a "
                   'répondu à mes demandes et à mes messages avec une rapidité '
                   'exemplaire je suis très satisfait merci beaucoup.',
 'review_datetime': '2025.06.06 02:24',
 'review_has_answer': False,
 'review_id': '38679597',
 'reviewer_id': 'dnamron',
 'reviewer_name': 'Dnamron',
 'reviewer_score': 66,
 'scraped_at': '2025-06-13T15:54:54.044726',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '"This is an EA that allows you to trade with great peace '
                   'of mind." 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:43 # Thank you so much! That’s exactly what '
                   'I wanted to achieve with Quantum Queen — an EA that brings '
                   'not just results, but also peace of mind. Trading '
                   'shouldn’t be stressful, and I’m really glad you’re feeling '
                   'that sense of calm while using it. If you ever have '
                   'questions or need anything, feel free to reach out '
                   'anytime!',
 'review_datetime': '2025.06.04 08:33',
 'review_has_answer': False,
 'review_id': '38661377',
 'reviewer_id': 'jza80spl',
 'reviewer_name': 'jza80spl',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:54:54.049715',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '378 Guorong He 2025.06.03 16:06 # Very good grid system. '
                   'Thanks Bogdan！ 34750 Reply from developer Bogdan Ion '
                   'Puscasu 2025.06.05 14:42 # Thank you so much for your '
                   "review! I’m really glad to hear you're enjoying the grid "
                   'system. It’s designed to be both smart and steady, and I’m '
                   'happy it’s working well for you. Appreciate the kind words '
                   '– and if you ever need help or want to explore more '
                   'Quantum EAs, I’m just a message away!',
 'review_datetime': '2025.06.03 16:06',
 'review_has_answer': False,
 'review_id': '38691977',
 'reviewer_id': 'tokio147',
 'reviewer_name': 'Guorong He',
 'reviewer_score': 378,
 'scraped_at': '2025-06-13T15:54:54.053018',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.02 21:18',
 'review_has_answer': False,
 'review_id': '38596644',
 'reviewer_id': 'molobamllr',
 'reviewer_name': 'Lorenz Goeminne',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:54:54.057064',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bought the EA last week and already made about 4% in just '
                   'one week! Really happy with the performance so far. The '
                   'documentation provided is very detailed and informative — '
                   'it made setup and understanding the strategy very easy. '
                   'Looking forward to seeing more great results. 34750 Reply '
                   'from developer Bogdan Ion Puscasu 2025.06.05 14:37 # '
                   "That's fantastic to hear! I'm really glad the setup "
                   "process went smoothly and that you're already seeing solid "
                   'results in just one week — 4% is a great start! Thanks for '
                   'pointing out the documentation too, I put a lot of effort '
                   'into making it as clear and helpful as possible. Wishing '
                   'you continued success, and if you ever need anything, '
                   'don’t hesitate to reach out.',
 'review_datetime': '2025.06.02 13:36',
 'review_has_answer': False,
 'review_id': '38600643',
 'reviewer_id': 'nasoo',
 'reviewer_name': 'Nasoo',
 'reviewer_score': 785,
 'scraped_at': '2025-06-13T15:54:54.063455',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I am very satisfied with the results from the 1st week, '
                   'its the same as in the backtests. I fully recommend it. '
                   '34750 Reply from developer Bogdan Ion Puscasu 2025.06.05 '
                   "14:35 # Thank you so much for the great feedback! I'm "
                   'really glad to hear that your first week with Quantum '
                   "Queen matches the backtests — that's always a great sign. "
                   "It means a lot to know you're satisfied and confident in "
                   'the EA. Wishing you continued success and steady profits '
                   "ahead! If you ever need help or have questions, I'm here.",
 'review_datetime': '2025.06.01 15:52',
 'review_has_answer': False,
 'review_id': '38580848',
 'reviewer_id': 'francesc00',
 'reviewer_name': 'Francesc00',
 'reviewer_score': 34,
 'scraped_at': '2025-06-13T15:54:54.067081',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.05.31 06:14',
 'review_has_answer': False,
 'review_id': '38668358',
 'reviewer_id': 'schnitzler88',
 'reviewer_name': 'Extoflex',
 'reviewer_score': 328,
 'scraped_at': '2025-06-13T15:54:54.071151',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '40 Lucas Ramos 2025.05.30 09:43 # O Bogdan é um Trader '
                   'extremamente responsável e simpático e oferece suporte '
                   'rápido, a Quantum Queen é uma ótima EA que já me rendeu '
                   'bons lucros com baixos riscos, recomendo fortemente o uso '
                   'dessa EA para Traders responsáveis e que queiram fazer seu '
                   'dinheiro render sem muito esforço 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:33 # Thank you '
                   "so much for your kind words and trust, Lucas! 🙏 I'm really "
                   'happy to hear that Quantum Queen is delivering good '
                   'profits with low risk for you — exactly what I designed it '
                   'for. I truly appreciate your support and recommendation, '
                   "and I'm always here if you need help or have any "
                   'questions. Wishing you continued success and steady growth '
                   "— let's keep making the most out of every trade!",
 'review_datetime': '2025.05.30 09:43',
 'review_has_answer': False,
 'review_id': '38537336',
 'reviewer_id': 'lucasramoss',
 'reviewer_name': 'Lucas Ramos',
 'reviewer_score': 40,
 'scraped_at': '2025-06-13T15:54:54.076735',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bogdan has been incredibly responsive, often replying '
                   'within minutes, which makes it feel like I have a tech '
                   'team right beside me — something I really appreciate. I’ve '
                   'been running the EA from May 26th to May 29th, and it '
                   'delivered a 2% profit within just four days using the '
                   'default risk settings. It doesn’t use a martingale '
                   'strategy, but it does attempt to average price levels in a '
                   'controlled manner. I especially like that it only executes '
                   'trades when specific market conditions are met, aiming for '
                   'higher probability setups. So far, the performance has '
                   'been solid and feels safe. Highly recommended, and based '
                   'on these results, I plan to explore more of their '
                   'products. 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:31 # Thank you so much for the thoughtful '
                   'and detailed feedback, Leo! 🙏 I’m really glad to hear that '
                   'you appreciate the way Quantum Queen handles trades — '
                   'focused, careful, and without martingale risk. I’ve spent '
                   'a lot of time fine-tuning the strategy to be both smart '
                   'and safe, and it’s great to know it’s performing well for '
                   'you. I’m also always happy to help, so don’t hesitate to '
                   'reach out anytime. Looking forward to having you explore '
                   'the rest of the Quantum family — there’s more good stuff '
                   'waiting!',
 'review_datetime': '2025.05.30 08:56',
 'review_has_answer': False,
 'review_id': '38237707',
 'reviewer_id': 'chasuhun23',
 'reviewer_name': 'Leo',
 'reviewer_score': 56,
 'scraped_at': '2025-06-13T15:54:54.080175',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '24 Michael Schnitzer 2025.05.29 17:57 # I can highly '
                   'recommend the Queen. You get an immediate response to any '
                   'kind of question, and the system truly works. Thank you, '
                   'Bogdan! 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:29 # Thank you so much for the wonderful '
                   'feedback! I’m really happy to hear that Quantum Queen is '
                   'working well for you and that you feel supported — I’m '
                   'always here to help whenever you need. Wishing you '
                   'continued success and steady profits!',
 'review_datetime': '2025.05.29 17:57',
 'review_has_answer': False,
 'review_id': '38508546',
 'reviewer_id': 'm_schnitzer',
 'reviewer_name': 'Michael Schnitzer',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:54:54.083267',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '35 Steels is 2025.05.29 12:33 # "It is a great system and '
                   'a profitable, thank you Bogdan! 34750 Reply from developer '
                   'Bogdan Ion Puscasu 2025.06.05 14:28 # Thank you so much '
                   'for your kind words! I’m really glad to hear you’re seeing '
                   'great results with the system. That’s exactly what I aimed '
                   'for when building Quantum Queen. Your support means a lot '
                   '— thank you!',
 'review_datetime': '2025.05.29 12:33',
 'review_has_answer': False,
 'review_id': '38517999',
 'reviewer_id': 'steelis',
 'reviewer_name': 'Steels is',
 'reviewer_score': 35,
 'scraped_at': '2025-06-13T15:54:54.089283',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:54 [review] INFO: Parsing reviews page 5 for product 118805
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Backtest results correlate very well with live '
                   'performance. Owner is super responsive and the Quantum '
                   'community is supportive and helpful as well. This has been '
                   'an excellent experience. Bought it on late May and profit '
                   'has already covered the cost of this EA (including VPS '
                   'cost). If you manage your risk and be patient, it just '
                   'pays itself off. This is as close you can get to an golden '
                   'goose.',
 'review_datetime': '2025.06.12 15:58',
 'review_has_answer': False,
 'review_id': '38658708',
 'reviewer_id': 'tiltedk',
 'reviewer_name': 'Thanos',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:54:54.994394',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 11:50',
 'review_has_answer': False,
 'review_id': '38766955',
 'reviewer_id': 'daryanahmmed',
 'reviewer_name': 'Daryan Ahmed',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:54:54.997264',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 04:27',
 'review_has_answer': False,
 'review_id': '38826636',
 'reviewer_id': 'chalermpetchara',
 'reviewer_name': 'chalerm petcharat',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:54:55.000209',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '599 Pitt Petruschke 2025.06.11 09:21 # ich nutze den EA '
                   'jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das '
                   'bedeutet, der macht wenige aber dafür profitabele Trades, '
                   'was meinem Trading Stil entgegenkommt. Der Support '
                   'arbeitet super schnell...',
 'review_datetime': '2025.06.11 09:21',
 'review_has_answer': False,
 'review_id': '38655413',
 'reviewer_id': 'pablo1904',
 'reviewer_name': 'Pitt Petruschke',
 'reviewer_score': 599,
 'scraped_at': '2025-06-13T15:54:55.002294',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I decided to leave a review after 2-3 weeks of testing on '
                   'a live account. I really like the way QQ5 works. Deals are '
                   'opened not very often, but qualitatively and I understand '
                   'that this is due to the fact that EA waits for the perfect '
                   'moment, taking into account the market situation. And when '
                   'it opens a deal, then another one and another and '
                   'eventually closes almost all deals with a profit. I have '
                   'never had a series of trades that went into the minus, '
                   'always in the plus. My settings are automatic with medium '
                   'risk. My broker is ByBit. I am very happy with the result '
                   "of QQ5's work, I will continue to trust him to manage my "
                   'budget. Thank you for such a quality EA! I recommend '
                   'everyone to buy it, the price matches the quality - and it '
                   'will definitely pay off!',
 'review_datetime': '2025.06.11 08:19',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'kirilka59',
 'reviewer_name': 'Kirilka59',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:54:55.004393',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '48 goateeeth maker 2025.06.08 02:25 # ⭐⭐⭐⭐⭐ Excellent '
                   "EA—Profitable and Reliable! I've been using this EA on MT5 "
                   "for several weeks, and it's exceeded my expectations. The "
                   'performance has been consistent, generating steady profits '
                   'while effectively managing risks. Setup was '
                   'straightforward, and customer support has been responsive '
                   'whenever I had questions. Highly recommend this EA to '
                   'anyone looking for an automated, stress-free trading '
                   'solution!',
 'review_datetime': '2025.06.08 02:25',
 'review_has_answer': False,
 'review_id': '38333720',
 'reviewer_id': 'goateeeth',
 'reviewer_name': 'goateeeth maker',
 'reviewer_score': 48,
 'scraped_at': '2025-06-13T15:54:55.006827',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '🌟 An Expert Advisor That Truly Delivers! 🌟 I’ve been using '
                   'Quantum Queen MT5 for a short time, but I’m already seeing '
                   'my account grow steadily. A colleague of mine has been '
                   'using it for over two months, and his results have been '
                   'exceptional and consistent, without hesitation. 🔹 It’s one '
                   'of the very few algobots on MetaTrader 5 that actually '
                   'works. 🔹 The developer is very professional and always '
                   'quick to respond to any questions. 🔹 It was recommended to '
                   'me by someone extremely meticulous… and it has exceeded '
                   'all expectations. 💰 It’s worth every cent of the price. '
                   'This is a professional tool, not just another empty '
                   'promise. 🔒 Reliable, profitable, and well-supported. '
                   'Highly recommended!',
 'review_datetime': '2025.06.06 23:02',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'saman83',
 'reviewer_name': 'Saman83',
 'reviewer_score': 79,
 'scraped_at': '2025-06-13T15:54:55.010348',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '53 SAMUEL DE SOUZA 2025.06.06 22:12 # Absolutely '
                   'Phenomenal – A True Queen in the Game! I’ve tested over 40 '
                   'different AEs in my trading journey, and I can confidently '
                   'say the Quantum Queen MT5 is in a league of its own. It’s '
                   'not just another gold (XAUUSD) AE—it’s a '
                   'precision-engineered powerhouse. From setup to execution, '
                   'everything runs smoothly and efficiently, adapting '
                   'perfectly to changing market conditions. Alongside The '
                   'Emperor, Quantum Queen is easily one of the two best AEs '
                   'I’ve ever come across. The consistency, reliability, and '
                   'intelligent trade management this bot brings are '
                   'unmatched. Whether you’re a seasoned trader or just '
                   'starting out, this is a must-have for your arsenal. Highly '
                   'recommend to anyone serious about mastering gold on MT5!',
 'review_datetime': '2025.06.06 22:12',
 'review_has_answer': False,
 'review_id': '38737428',
 'reviewer_id': 'swtsam2',
 'reviewer_name': 'SAMUEL DE SOUZA',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:54:55.012871',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '购买这个EA之后我基于最近半年短期做了很多次回测 根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 '
                   '每一次都是盈利的 而且都是非常可观的 用我们国家的一句老话来说就是 兵欲善其事 必先利其器 '
                   '我觉得QQ对我这个新手来说十分友好 并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的',
 'review_datetime': '2025.06.06 20:33',
 'review_has_answer': False,
 'review_id': '38705502',
 'reviewer_id': 'noalc',
 'reviewer_name': 'Noalc',
 'reviewer_score': 22,
 'scraped_at': '2025-06-13T15:54:55.014939',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "Bonjour je viens d'acheter expert Quantum Queen MT5 c'est "
                   "un très bon expert il est très stable il n'y a des perte "
                   'que très minime et donne plus de bon profit je le trouve '
                   'excellent. Très bon service après vente pour '
                   "l'installation et les conseils de Bogdan Ion Puscasu il a "
                   'répondu à mes demandes et à mes messages avec une rapidité '
                   'exemplaire je suis très satisfait merci beaucoup.',
 'review_datetime': '2025.06.06 02:24',
 'review_has_answer': False,
 'review_id': '38679597',
 'reviewer_id': 'dnamron',
 'reviewer_name': 'Dnamron',
 'reviewer_score': 66,
 'scraped_at': '2025-06-13T15:54:55.017483',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '"This is an EA that allows you to trade with great peace '
                   'of mind." 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:43 # Thank you so much! That’s exactly what '
                   'I wanted to achieve with Quantum Queen — an EA that brings '
                   'not just results, but also peace of mind. Trading '
                   'shouldn’t be stressful, and I’m really glad you’re feeling '
                   'that sense of calm while using it. If you ever have '
                   'questions or need anything, feel free to reach out '
                   'anytime!',
 'review_datetime': '2025.06.04 08:33',
 'review_has_answer': False,
 'review_id': '38661377',
 'reviewer_id': 'jza80spl',
 'reviewer_name': 'jza80spl',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:54:55.020445',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '378 Guorong He 2025.06.03 16:06 # Very good grid system. '
                   'Thanks Bogdan！ 34750 Reply from developer Bogdan Ion '
                   'Puscasu 2025.06.05 14:42 # Thank you so much for your '
                   "review! I’m really glad to hear you're enjoying the grid "
                   'system. It’s designed to be both smart and steady, and I’m '
                   'happy it’s working well for you. Appreciate the kind words '
                   '– and if you ever need help or want to explore more '
                   'Quantum EAs, I’m just a message away!',
 'review_datetime': '2025.06.03 16:06',
 'review_has_answer': False,
 'review_id': '38691977',
 'reviewer_id': 'tokio147',
 'reviewer_name': 'Guorong He',
 'reviewer_score': 378,
 'scraped_at': '2025-06-13T15:54:55.026308',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.02 21:18',
 'review_has_answer': False,
 'review_id': '38596644',
 'reviewer_id': 'molobamllr',
 'reviewer_name': 'Lorenz Goeminne',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:54:55.029897',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bought the EA last week and already made about 4% in just '
                   'one week! Really happy with the performance so far. The '
                   'documentation provided is very detailed and informative — '
                   'it made setup and understanding the strategy very easy. '
                   'Looking forward to seeing more great results. 34750 Reply '
                   'from developer Bogdan Ion Puscasu 2025.06.05 14:37 # '
                   "That's fantastic to hear! I'm really glad the setup "
                   "process went smoothly and that you're already seeing solid "
                   'results in just one week — 4% is a great start! Thanks for '
                   'pointing out the documentation too, I put a lot of effort '
                   'into making it as clear and helpful as possible. Wishing '
                   'you continued success, and if you ever need anything, '
                   'don’t hesitate to reach out.',
 'review_datetime': '2025.06.02 13:36',
 'review_has_answer': False,
 'review_id': '38600643',
 'reviewer_id': 'nasoo',
 'reviewer_name': 'Nasoo',
 'reviewer_score': 785,
 'scraped_at': '2025-06-13T15:54:55.033061',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I am very satisfied with the results from the 1st week, '
                   'its the same as in the backtests. I fully recommend it. '
                   '34750 Reply from developer Bogdan Ion Puscasu 2025.06.05 '
                   "14:35 # Thank you so much for the great feedback! I'm "
                   'really glad to hear that your first week with Quantum '
                   "Queen matches the backtests — that's always a great sign. "
                   "It means a lot to know you're satisfied and confident in "
                   'the EA. Wishing you continued success and steady profits '
                   "ahead! If you ever need help or have questions, I'm here.",
 'review_datetime': '2025.06.01 15:52',
 'review_has_answer': False,
 'review_id': '38580848',
 'reviewer_id': 'francesc00',
 'reviewer_name': 'Francesc00',
 'reviewer_score': 34,
 'scraped_at': '2025-06-13T15:54:55.036139',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.05.31 06:14',
 'review_has_answer': False,
 'review_id': '38668358',
 'reviewer_id': 'schnitzler88',
 'reviewer_name': 'Extoflex',
 'reviewer_score': 328,
 'scraped_at': '2025-06-13T15:54:55.043013',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '40 Lucas Ramos 2025.05.30 09:43 # O Bogdan é um Trader '
                   'extremamente responsável e simpático e oferece suporte '
                   'rápido, a Quantum Queen é uma ótima EA que já me rendeu '
                   'bons lucros com baixos riscos, recomendo fortemente o uso '
                   'dessa EA para Traders responsáveis e que queiram fazer seu '
                   'dinheiro render sem muito esforço 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:33 # Thank you '
                   "so much for your kind words and trust, Lucas! 🙏 I'm really "
                   'happy to hear that Quantum Queen is delivering good '
                   'profits with low risk for you — exactly what I designed it '
                   'for. I truly appreciate your support and recommendation, '
                   "and I'm always here if you need help or have any "
                   'questions. Wishing you continued success and steady growth '
                   "— let's keep making the most out of every trade!",
 'review_datetime': '2025.05.30 09:43',
 'review_has_answer': False,
 'review_id': '38537336',
 'reviewer_id': 'lucasramoss',
 'reviewer_name': 'Lucas Ramos',
 'reviewer_score': 40,
 'scraped_at': '2025-06-13T15:54:55.045724',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bogdan has been incredibly responsive, often replying '
                   'within minutes, which makes it feel like I have a tech '
                   'team right beside me — something I really appreciate. I’ve '
                   'been running the EA from May 26th to May 29th, and it '
                   'delivered a 2% profit within just four days using the '
                   'default risk settings. It doesn’t use a martingale '
                   'strategy, but it does attempt to average price levels in a '
                   'controlled manner. I especially like that it only executes '
                   'trades when specific market conditions are met, aiming for '
                   'higher probability setups. So far, the performance has '
                   'been solid and feels safe. Highly recommended, and based '
                   'on these results, I plan to explore more of their '
                   'products. 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:31 # Thank you so much for the thoughtful '
                   'and detailed feedback, Leo! 🙏 I’m really glad to hear that '
                   'you appreciate the way Quantum Queen handles trades — '
                   'focused, careful, and without martingale risk. I’ve spent '
                   'a lot of time fine-tuning the strategy to be both smart '
                   'and safe, and it’s great to know it’s performing well for '
                   'you. I’m also always happy to help, so don’t hesitate to '
                   'reach out anytime. Looking forward to having you explore '
                   'the rest of the Quantum family — there’s more good stuff '
                   'waiting!',
 'review_datetime': '2025.05.30 08:56',
 'review_has_answer': False,
 'review_id': '38237707',
 'reviewer_id': 'chasuhun23',
 'reviewer_name': 'Leo',
 'reviewer_score': 56,
 'scraped_at': '2025-06-13T15:54:55.050852',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '24 Michael Schnitzer 2025.05.29 17:57 # I can highly '
                   'recommend the Queen. You get an immediate response to any '
                   'kind of question, and the system truly works. Thank you, '
                   'Bogdan! 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:29 # Thank you so much for the wonderful '
                   'feedback! I’m really happy to hear that Quantum Queen is '
                   'working well for you and that you feel supported — I’m '
                   'always here to help whenever you need. Wishing you '
                   'continued success and steady profits!',
 'review_datetime': '2025.05.29 17:57',
 'review_has_answer': False,
 'review_id': '38508546',
 'reviewer_id': 'm_schnitzer',
 'reviewer_name': 'Michael Schnitzer',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:54:55.056832',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '35 Steels is 2025.05.29 12:33 # "It is a great system and '
                   'a profitable, thank you Bogdan! 34750 Reply from developer '
                   'Bogdan Ion Puscasu 2025.06.05 14:28 # Thank you so much '
                   'for your kind words! I’m really glad to hear you’re seeing '
                   'great results with the system. That’s exactly what I aimed '
                   'for when building Quantum Queen. Your support means a lot '
                   '— thank you!',
 'review_datetime': '2025.05.29 12:33',
 'review_has_answer': False,
 'review_id': '38517999',
 'reviewer_id': 'steelis',
 'reviewer_name': 'Steels is',
 'reviewer_score': 35,
 'scraped_at': '2025-06-13T15:54:55.059881',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:55 [review] INFO: Parsing reviews page 4 for product 118805
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Backtest results correlate very well with live '
                   'performance. Owner is super responsive and the Quantum '
                   'community is supportive and helpful as well. This has been '
                   'an excellent experience. Bought it on late May and profit '
                   'has already covered the cost of this EA (including VPS '
                   'cost). If you manage your risk and be patient, it just '
                   'pays itself off. This is as close you can get to an golden '
                   'goose.',
 'review_datetime': '2025.06.12 15:58',
 'review_has_answer': False,
 'review_id': '38658708',
 'reviewer_id': 'tiltedk',
 'reviewer_name': 'Thanos',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:54:55.998381',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 11:50',
 'review_has_answer': False,
 'review_id': '38766955',
 'reviewer_id': 'daryanahmmed',
 'reviewer_name': 'Daryan Ahmed',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:54:56.001455',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 04:27',
 'review_has_answer': False,
 'review_id': '38826636',
 'reviewer_id': 'chalermpetchara',
 'reviewer_name': 'chalerm petcharat',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:54:56.004013',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '599 Pitt Petruschke 2025.06.11 09:21 # ich nutze den EA '
                   'jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das '
                   'bedeutet, der macht wenige aber dafür profitabele Trades, '
                   'was meinem Trading Stil entgegenkommt. Der Support '
                   'arbeitet super schnell...',
 'review_datetime': '2025.06.11 09:21',
 'review_has_answer': False,
 'review_id': '38655413',
 'reviewer_id': 'pablo1904',
 'reviewer_name': 'Pitt Petruschke',
 'reviewer_score': 599,
 'scraped_at': '2025-06-13T15:54:56.006819',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I decided to leave a review after 2-3 weeks of testing on '
                   'a live account. I really like the way QQ5 works. Deals are '
                   'opened not very often, but qualitatively and I understand '
                   'that this is due to the fact that EA waits for the perfect '
                   'moment, taking into account the market situation. And when '
                   'it opens a deal, then another one and another and '
                   'eventually closes almost all deals with a profit. I have '
                   'never had a series of trades that went into the minus, '
                   'always in the plus. My settings are automatic with medium '
                   'risk. My broker is ByBit. I am very happy with the result '
                   "of QQ5's work, I will continue to trust him to manage my "
                   'budget. Thank you for such a quality EA! I recommend '
                   'everyone to buy it, the price matches the quality - and it '
                   'will definitely pay off!',
 'review_datetime': '2025.06.11 08:19',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'kirilka59',
 'reviewer_name': 'Kirilka59',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:54:56.009183',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '48 goateeeth maker 2025.06.08 02:25 # ⭐⭐⭐⭐⭐ Excellent '
                   "EA—Profitable and Reliable! I've been using this EA on MT5 "
                   "for several weeks, and it's exceeded my expectations. The "
                   'performance has been consistent, generating steady profits '
                   'while effectively managing risks. Setup was '
                   'straightforward, and customer support has been responsive '
                   'whenever I had questions. Highly recommend this EA to '
                   'anyone looking for an automated, stress-free trading '
                   'solution!',
 'review_datetime': '2025.06.08 02:25',
 'review_has_answer': False,
 'review_id': '38333720',
 'reviewer_id': 'goateeeth',
 'reviewer_name': 'goateeeth maker',
 'reviewer_score': 48,
 'scraped_at': '2025-06-13T15:54:56.012948',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '🌟 An Expert Advisor That Truly Delivers! 🌟 I’ve been using '
                   'Quantum Queen MT5 for a short time, but I’m already seeing '
                   'my account grow steadily. A colleague of mine has been '
                   'using it for over two months, and his results have been '
                   'exceptional and consistent, without hesitation. 🔹 It’s one '
                   'of the very few algobots on MetaTrader 5 that actually '
                   'works. 🔹 The developer is very professional and always '
                   'quick to respond to any questions. 🔹 It was recommended to '
                   'me by someone extremely meticulous… and it has exceeded '
                   'all expectations. 💰 It’s worth every cent of the price. '
                   'This is a professional tool, not just another empty '
                   'promise. 🔒 Reliable, profitable, and well-supported. '
                   'Highly recommended!',
 'review_datetime': '2025.06.06 23:02',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'saman83',
 'reviewer_name': 'Saman83',
 'reviewer_score': 79,
 'scraped_at': '2025-06-13T15:54:56.015162',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '53 SAMUEL DE SOUZA 2025.06.06 22:12 # Absolutely '
                   'Phenomenal – A True Queen in the Game! I’ve tested over 40 '
                   'different AEs in my trading journey, and I can confidently '
                   'say the Quantum Queen MT5 is in a league of its own. It’s '
                   'not just another gold (XAUUSD) AE—it’s a '
                   'precision-engineered powerhouse. From setup to execution, '
                   'everything runs smoothly and efficiently, adapting '
                   'perfectly to changing market conditions. Alongside The '
                   'Emperor, Quantum Queen is easily one of the two best AEs '
                   'I’ve ever come across. The consistency, reliability, and '
                   'intelligent trade management this bot brings are '
                   'unmatched. Whether you’re a seasoned trader or just '
                   'starting out, this is a must-have for your arsenal. Highly '
                   'recommend to anyone serious about mastering gold on MT5!',
 'review_datetime': '2025.06.06 22:12',
 'review_has_answer': False,
 'review_id': '38737428',
 'reviewer_id': 'swtsam2',
 'reviewer_name': 'SAMUEL DE SOUZA',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:54:56.017406',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '购买这个EA之后我基于最近半年短期做了很多次回测 根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 '
                   '每一次都是盈利的 而且都是非常可观的 用我们国家的一句老话来说就是 兵欲善其事 必先利其器 '
                   '我觉得QQ对我这个新手来说十分友好 并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的',
 'review_datetime': '2025.06.06 20:33',
 'review_has_answer': False,
 'review_id': '38705502',
 'reviewer_id': 'noalc',
 'reviewer_name': 'Noalc',
 'reviewer_score': 22,
 'scraped_at': '2025-06-13T15:54:56.019443',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "Bonjour je viens d'acheter expert Quantum Queen MT5 c'est "
                   "un très bon expert il est très stable il n'y a des perte "
                   'que très minime et donne plus de bon profit je le trouve '
                   'excellent. Très bon service après vente pour '
                   "l'installation et les conseils de Bogdan Ion Puscasu il a "
                   'répondu à mes demandes et à mes messages avec une rapidité '
                   'exemplaire je suis très satisfait merci beaucoup.',
 'review_datetime': '2025.06.06 02:24',
 'review_has_answer': False,
 'review_id': '38679597',
 'reviewer_id': 'dnamron',
 'reviewer_name': 'Dnamron',
 'reviewer_score': 66,
 'scraped_at': '2025-06-13T15:54:56.021427',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '"This is an EA that allows you to trade with great peace '
                   'of mind." 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:43 # Thank you so much! That’s exactly what '
                   'I wanted to achieve with Quantum Queen — an EA that brings '
                   'not just results, but also peace of mind. Trading '
                   'shouldn’t be stressful, and I’m really glad you’re feeling '
                   'that sense of calm while using it. If you ever have '
                   'questions or need anything, feel free to reach out '
                   'anytime!',
 'review_datetime': '2025.06.04 08:33',
 'review_has_answer': False,
 'review_id': '38661377',
 'reviewer_id': 'jza80spl',
 'reviewer_name': 'jza80spl',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:54:56.026860',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '378 Guorong He 2025.06.03 16:06 # Very good grid system. '
                   'Thanks Bogdan！ 34750 Reply from developer Bogdan Ion '
                   'Puscasu 2025.06.05 14:42 # Thank you so much for your '
                   "review! I’m really glad to hear you're enjoying the grid "
                   'system. It’s designed to be both smart and steady, and I’m '
                   'happy it’s working well for you. Appreciate the kind words '
                   '– and if you ever need help or want to explore more '
                   'Quantum EAs, I’m just a message away!',
 'review_datetime': '2025.06.03 16:06',
 'review_has_answer': False,
 'review_id': '38691977',
 'reviewer_id': 'tokio147',
 'reviewer_name': 'Guorong He',
 'reviewer_score': 378,
 'scraped_at': '2025-06-13T15:54:56.030279',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.02 21:18',
 'review_has_answer': False,
 'review_id': '38596644',
 'reviewer_id': 'molobamllr',
 'reviewer_name': 'Lorenz Goeminne',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:54:56.033976',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bought the EA last week and already made about 4% in just '
                   'one week! Really happy with the performance so far. The '
                   'documentation provided is very detailed and informative — '
                   'it made setup and understanding the strategy very easy. '
                   'Looking forward to seeing more great results. 34750 Reply '
                   'from developer Bogdan Ion Puscasu 2025.06.05 14:37 # '
                   "That's fantastic to hear! I'm really glad the setup "
                   "process went smoothly and that you're already seeing solid "
                   'results in just one week — 4% is a great start! Thanks for '
                   'pointing out the documentation too, I put a lot of effort '
                   'into making it as clear and helpful as possible. Wishing '
                   'you continued success, and if you ever need anything, '
                   'don’t hesitate to reach out.',
 'review_datetime': '2025.06.02 13:36',
 'review_has_answer': False,
 'review_id': '38600643',
 'reviewer_id': 'nasoo',
 'reviewer_name': 'Nasoo',
 'reviewer_score': 785,
 'scraped_at': '2025-06-13T15:54:56.041236',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I am very satisfied with the results from the 1st week, '
                   'its the same as in the backtests. I fully recommend it. '
                   '34750 Reply from developer Bogdan Ion Puscasu 2025.06.05 '
                   "14:35 # Thank you so much for the great feedback! I'm "
                   'really glad to hear that your first week with Quantum '
                   "Queen matches the backtests — that's always a great sign. "
                   "It means a lot to know you're satisfied and confident in "
                   'the EA. Wishing you continued success and steady profits '
                   "ahead! If you ever need help or have questions, I'm here.",
 'review_datetime': '2025.06.01 15:52',
 'review_has_answer': False,
 'review_id': '38580848',
 'reviewer_id': 'francesc00',
 'reviewer_name': 'Francesc00',
 'reviewer_score': 34,
 'scraped_at': '2025-06-13T15:54:56.044388',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.05.31 06:14',
 'review_has_answer': False,
 'review_id': '38668358',
 'reviewer_id': 'schnitzler88',
 'reviewer_name': 'Extoflex',
 'reviewer_score': 328,
 'scraped_at': '2025-06-13T15:54:56.048548',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '40 Lucas Ramos 2025.05.30 09:43 # O Bogdan é um Trader '
                   'extremamente responsável e simpático e oferece suporte '
                   'rápido, a Quantum Queen é uma ótima EA que já me rendeu '
                   'bons lucros com baixos riscos, recomendo fortemente o uso '
                   'dessa EA para Traders responsáveis e que queiram fazer seu '
                   'dinheiro render sem muito esforço 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:33 # Thank you '
                   "so much for your kind words and trust, Lucas! 🙏 I'm really "
                   'happy to hear that Quantum Queen is delivering good '
                   'profits with low risk for you — exactly what I designed it '
                   'for. I truly appreciate your support and recommendation, '
                   "and I'm always here if you need help or have any "
                   'questions. Wishing you continued success and steady growth '
                   "— let's keep making the most out of every trade!",
 'review_datetime': '2025.05.30 09:43',
 'review_has_answer': False,
 'review_id': '38537336',
 'reviewer_id': 'lucasramoss',
 'reviewer_name': 'Lucas Ramos',
 'reviewer_score': 40,
 'scraped_at': '2025-06-13T15:54:56.054129',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bogdan has been incredibly responsive, often replying '
                   'within minutes, which makes it feel like I have a tech '
                   'team right beside me — something I really appreciate. I’ve '
                   'been running the EA from May 26th to May 29th, and it '
                   'delivered a 2% profit within just four days using the '
                   'default risk settings. It doesn’t use a martingale '
                   'strategy, but it does attempt to average price levels in a '
                   'controlled manner. I especially like that it only executes '
                   'trades when specific market conditions are met, aiming for '
                   'higher probability setups. So far, the performance has '
                   'been solid and feels safe. Highly recommended, and based '
                   'on these results, I plan to explore more of their '
                   'products. 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:31 # Thank you so much for the thoughtful '
                   'and detailed feedback, Leo! 🙏 I’m really glad to hear that '
                   'you appreciate the way Quantum Queen handles trades — '
                   'focused, careful, and without martingale risk. I’ve spent '
                   'a lot of time fine-tuning the strategy to be both smart '
                   'and safe, and it’s great to know it’s performing well for '
                   'you. I’m also always happy to help, so don’t hesitate to '
                   'reach out anytime. Looking forward to having you explore '
                   'the rest of the Quantum family — there’s more good stuff '
                   'waiting!',
 'review_datetime': '2025.05.30 08:56',
 'review_has_answer': False,
 'review_id': '38237707',
 'reviewer_id': 'chasuhun23',
 'reviewer_name': 'Leo',
 'reviewer_score': 56,
 'scraped_at': '2025-06-13T15:54:56.057160',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '24 Michael Schnitzer 2025.05.29 17:57 # I can highly '
                   'recommend the Queen. You get an immediate response to any '
                   'kind of question, and the system truly works. Thank you, '
                   'Bogdan! 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:29 # Thank you so much for the wonderful '
                   'feedback! I’m really happy to hear that Quantum Queen is '
                   'working well for you and that you feel supported — I’m '
                   'always here to help whenever you need. Wishing you '
                   'continued success and steady profits!',
 'review_datetime': '2025.05.29 17:57',
 'review_has_answer': False,
 'review_id': '38508546',
 'reviewer_id': 'm_schnitzer',
 'reviewer_name': 'Michael Schnitzer',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:54:56.061164',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '35 Steels is 2025.05.29 12:33 # "It is a great system and '
                   'a profitable, thank you Bogdan! 34750 Reply from developer '
                   'Bogdan Ion Puscasu 2025.06.05 14:28 # Thank you so much '
                   'for your kind words! I’m really glad to hear you’re seeing '
                   'great results with the system. That’s exactly what I aimed '
                   'for when building Quantum Queen. Your support means a lot '
                   '— thank you!',
 'review_datetime': '2025.05.29 12:33',
 'review_has_answer': False,
 'review_id': '38517999',
 'reviewer_id': 'steelis',
 'reviewer_name': 'Steels is',
 'reviewer_score': 35,
 'scraped_at': '2025-06-13T15:54:56.069663',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:56 [review] INFO: Parsing reviews page 3 for product 118805
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Backtest results correlate very well with live '
                   'performance. Owner is super responsive and the Quantum '
                   'community is supportive and helpful as well. This has been '
                   'an excellent experience. Bought it on late May and profit '
                   'has already covered the cost of this EA (including VPS '
                   'cost). If you manage your risk and be patient, it just '
                   'pays itself off. This is as close you can get to an golden '
                   'goose.',
 'review_datetime': '2025.06.12 15:58',
 'review_has_answer': False,
 'review_id': '38658708',
 'reviewer_id': 'tiltedk',
 'reviewer_name': 'Thanos',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:54:57.000617',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 11:50',
 'review_has_answer': False,
 'review_id': '38766955',
 'reviewer_id': 'daryanahmmed',
 'reviewer_name': 'Daryan Ahmed',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:54:57.003003',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 04:27',
 'review_has_answer': False,
 'review_id': '38826636',
 'reviewer_id': 'chalermpetchara',
 'reviewer_name': 'chalerm petcharat',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:54:57.006090',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '599 Pitt Petruschke 2025.06.11 09:21 # ich nutze den EA '
                   'jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das '
                   'bedeutet, der macht wenige aber dafür profitabele Trades, '
                   'was meinem Trading Stil entgegenkommt. Der Support '
                   'arbeitet super schnell...',
 'review_datetime': '2025.06.11 09:21',
 'review_has_answer': False,
 'review_id': '38655413',
 'reviewer_id': 'pablo1904',
 'reviewer_name': 'Pitt Petruschke',
 'reviewer_score': 599,
 'scraped_at': '2025-06-13T15:54:57.008221',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I decided to leave a review after 2-3 weeks of testing on '
                   'a live account. I really like the way QQ5 works. Deals are '
                   'opened not very often, but qualitatively and I understand '
                   'that this is due to the fact that EA waits for the perfect '
                   'moment, taking into account the market situation. And when '
                   'it opens a deal, then another one and another and '
                   'eventually closes almost all deals with a profit. I have '
                   'never had a series of trades that went into the minus, '
                   'always in the plus. My settings are automatic with medium '
                   'risk. My broker is ByBit. I am very happy with the result '
                   "of QQ5's work, I will continue to trust him to manage my "
                   'budget. Thank you for such a quality EA! I recommend '
                   'everyone to buy it, the price matches the quality - and it '
                   'will definitely pay off!',
 'review_datetime': '2025.06.11 08:19',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'kirilka59',
 'reviewer_name': 'Kirilka59',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:54:57.010117',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '48 goateeeth maker 2025.06.08 02:25 # ⭐⭐⭐⭐⭐ Excellent '
                   "EA—Profitable and Reliable! I've been using this EA on MT5 "
                   "for several weeks, and it's exceeded my expectations. The "
                   'performance has been consistent, generating steady profits '
                   'while effectively managing risks. Setup was '
                   'straightforward, and customer support has been responsive '
                   'whenever I had questions. Highly recommend this EA to '
                   'anyone looking for an automated, stress-free trading '
                   'solution!',
 'review_datetime': '2025.06.08 02:25',
 'review_has_answer': False,
 'review_id': '38333720',
 'reviewer_id': 'goateeeth',
 'reviewer_name': 'goateeeth maker',
 'reviewer_score': 48,
 'scraped_at': '2025-06-13T15:54:57.012396',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '🌟 An Expert Advisor That Truly Delivers! 🌟 I’ve been using '
                   'Quantum Queen MT5 for a short time, but I’m already seeing '
                   'my account grow steadily. A colleague of mine has been '
                   'using it for over two months, and his results have been '
                   'exceptional and consistent, without hesitation. 🔹 It’s one '
                   'of the very few algobots on MetaTrader 5 that actually '
                   'works. 🔹 The developer is very professional and always '
                   'quick to respond to any questions. 🔹 It was recommended to '
                   'me by someone extremely meticulous… and it has exceeded '
                   'all expectations. 💰 It’s worth every cent of the price. '
                   'This is a professional tool, not just another empty '
                   'promise. 🔒 Reliable, profitable, and well-supported. '
                   'Highly recommended!',
 'review_datetime': '2025.06.06 23:02',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'saman83',
 'reviewer_name': 'Saman83',
 'reviewer_score': 79,
 'scraped_at': '2025-06-13T15:54:57.015531',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '53 SAMUEL DE SOUZA 2025.06.06 22:12 # Absolutely '
                   'Phenomenal – A True Queen in the Game! I’ve tested over 40 '
                   'different AEs in my trading journey, and I can confidently '
                   'say the Quantum Queen MT5 is in a league of its own. It’s '
                   'not just another gold (XAUUSD) AE—it’s a '
                   'precision-engineered powerhouse. From setup to execution, '
                   'everything runs smoothly and efficiently, adapting '
                   'perfectly to changing market conditions. Alongside The '
                   'Emperor, Quantum Queen is easily one of the two best AEs '
                   'I’ve ever come across. The consistency, reliability, and '
                   'intelligent trade management this bot brings are '
                   'unmatched. Whether you’re a seasoned trader or just '
                   'starting out, this is a must-have for your arsenal. Highly '
                   'recommend to anyone serious about mastering gold on MT5!',
 'review_datetime': '2025.06.06 22:12',
 'review_has_answer': False,
 'review_id': '38737428',
 'reviewer_id': 'swtsam2',
 'reviewer_name': 'SAMUEL DE SOUZA',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:54:57.017813',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '购买这个EA之后我基于最近半年短期做了很多次回测 根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 '
                   '每一次都是盈利的 而且都是非常可观的 用我们国家的一句老话来说就是 兵欲善其事 必先利其器 '
                   '我觉得QQ对我这个新手来说十分友好 并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的',
 'review_datetime': '2025.06.06 20:33',
 'review_has_answer': False,
 'review_id': '38705502',
 'reviewer_id': 'noalc',
 'reviewer_name': 'Noalc',
 'reviewer_score': 22,
 'scraped_at': '2025-06-13T15:54:57.019915',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "Bonjour je viens d'acheter expert Quantum Queen MT5 c'est "
                   "un très bon expert il est très stable il n'y a des perte "
                   'que très minime et donne plus de bon profit je le trouve '
                   'excellent. Très bon service après vente pour '
                   "l'installation et les conseils de Bogdan Ion Puscasu il a "
                   'répondu à mes demandes et à mes messages avec une rapidité '
                   'exemplaire je suis très satisfait merci beaucoup.',
 'review_datetime': '2025.06.06 02:24',
 'review_has_answer': False,
 'review_id': '38679597',
 'reviewer_id': 'dnamron',
 'reviewer_name': 'Dnamron',
 'reviewer_score': 66,
 'scraped_at': '2025-06-13T15:54:57.021879',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '"This is an EA that allows you to trade with great peace '
                   'of mind." 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:43 # Thank you so much! That’s exactly what '
                   'I wanted to achieve with Quantum Queen — an EA that brings '
                   'not just results, but also peace of mind. Trading '
                   'shouldn’t be stressful, and I’m really glad you’re feeling '
                   'that sense of calm while using it. If you ever have '
                   'questions or need anything, feel free to reach out '
                   'anytime!',
 'review_datetime': '2025.06.04 08:33',
 'review_has_answer': False,
 'review_id': '38661377',
 'reviewer_id': 'jza80spl',
 'reviewer_name': 'jza80spl',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:54:57.024821',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '378 Guorong He 2025.06.03 16:06 # Very good grid system. '
                   'Thanks Bogdan！ 34750 Reply from developer Bogdan Ion '
                   'Puscasu 2025.06.05 14:42 # Thank you so much for your '
                   "review! I’m really glad to hear you're enjoying the grid "
                   'system. It’s designed to be both smart and steady, and I’m '
                   'happy it’s working well for you. Appreciate the kind words '
                   '– and if you ever need help or want to explore more '
                   'Quantum EAs, I’m just a message away!',
 'review_datetime': '2025.06.03 16:06',
 'review_has_answer': False,
 'review_id': '38691977',
 'reviewer_id': 'tokio147',
 'reviewer_name': 'Guorong He',
 'reviewer_score': 378,
 'scraped_at': '2025-06-13T15:54:57.031024',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.02 21:18',
 'review_has_answer': False,
 'review_id': '38596644',
 'reviewer_id': 'molobamllr',
 'reviewer_name': 'Lorenz Goeminne',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:54:57.034515',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bought the EA last week and already made about 4% in just '
                   'one week! Really happy with the performance so far. The '
                   'documentation provided is very detailed and informative — '
                   'it made setup and understanding the strategy very easy. '
                   'Looking forward to seeing more great results. 34750 Reply '
                   'from developer Bogdan Ion Puscasu 2025.06.05 14:37 # '
                   "That's fantastic to hear! I'm really glad the setup "
                   "process went smoothly and that you're already seeing solid "
                   'results in just one week — 4% is a great start! Thanks for '
                   'pointing out the documentation too, I put a lot of effort '
                   'into making it as clear and helpful as possible. Wishing '
                   'you continued success, and if you ever need anything, '
                   'don’t hesitate to reach out.',
 'review_datetime': '2025.06.02 13:36',
 'review_has_answer': False,
 'review_id': '38600643',
 'reviewer_id': 'nasoo',
 'reviewer_name': 'Nasoo',
 'reviewer_score': 785,
 'scraped_at': '2025-06-13T15:54:57.037520',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I am very satisfied with the results from the 1st week, '
                   'its the same as in the backtests. I fully recommend it. '
                   '34750 Reply from developer Bogdan Ion Puscasu 2025.06.05 '
                   "14:35 # Thank you so much for the great feedback! I'm "
                   'really glad to hear that your first week with Quantum '
                   "Queen matches the backtests — that's always a great sign. "
                   "It means a lot to know you're satisfied and confident in "
                   'the EA. Wishing you continued success and steady profits '
                   "ahead! If you ever need help or have questions, I'm here.",
 'review_datetime': '2025.06.01 15:52',
 'review_has_answer': False,
 'review_id': '38580848',
 'reviewer_id': 'francesc00',
 'reviewer_name': 'Francesc00',
 'reviewer_score': 34,
 'scraped_at': '2025-06-13T15:54:57.040199',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.05.31 06:14',
 'review_has_answer': False,
 'review_id': '38668358',
 'reviewer_id': 'schnitzler88',
 'reviewer_name': 'Extoflex',
 'reviewer_score': 328,
 'scraped_at': '2025-06-13T15:54:57.049479',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '40 Lucas Ramos 2025.05.30 09:43 # O Bogdan é um Trader '
                   'extremamente responsável e simpático e oferece suporte '
                   'rápido, a Quantum Queen é uma ótima EA que já me rendeu '
                   'bons lucros com baixos riscos, recomendo fortemente o uso '
                   'dessa EA para Traders responsáveis e que queiram fazer seu '
                   'dinheiro render sem muito esforço 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:33 # Thank you '
                   "so much for your kind words and trust, Lucas! 🙏 I'm really "
                   'happy to hear that Quantum Queen is delivering good '
                   'profits with low risk for you — exactly what I designed it '
                   'for. I truly appreciate your support and recommendation, '
                   "and I'm always here if you need help or have any "
                   'questions. Wishing you continued success and steady growth '
                   "— let's keep making the most out of every trade!",
 'review_datetime': '2025.05.30 09:43',
 'review_has_answer': False,
 'review_id': '38537336',
 'reviewer_id': 'lucasramoss',
 'reviewer_name': 'Lucas Ramos',
 'reviewer_score': 40,
 'scraped_at': '2025-06-13T15:54:57.052460',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bogdan has been incredibly responsive, often replying '
                   'within minutes, which makes it feel like I have a tech '
                   'team right beside me — something I really appreciate. I’ve '
                   'been running the EA from May 26th to May 29th, and it '
                   'delivered a 2% profit within just four days using the '
                   'default risk settings. It doesn’t use a martingale '
                   'strategy, but it does attempt to average price levels in a '
                   'controlled manner. I especially like that it only executes '
                   'trades when specific market conditions are met, aiming for '
                   'higher probability setups. So far, the performance has '
                   'been solid and feels safe. Highly recommended, and based '
                   'on these results, I plan to explore more of their '
                   'products. 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:31 # Thank you so much for the thoughtful '
                   'and detailed feedback, Leo! 🙏 I’m really glad to hear that '
                   'you appreciate the way Quantum Queen handles trades — '
                   'focused, careful, and without martingale risk. I’ve spent '
                   'a lot of time fine-tuning the strategy to be both smart '
                   'and safe, and it’s great to know it’s performing well for '
                   'you. I’m also always happy to help, so don’t hesitate to '
                   'reach out anytime. Looking forward to having you explore '
                   'the rest of the Quantum family — there’s more good stuff '
                   'waiting!',
 'review_datetime': '2025.05.30 08:56',
 'review_has_answer': False,
 'review_id': '38237707',
 'reviewer_id': 'chasuhun23',
 'reviewer_name': 'Leo',
 'reviewer_score': 56,
 'scraped_at': '2025-06-13T15:54:57.055486',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '24 Michael Schnitzer 2025.05.29 17:57 # I can highly '
                   'recommend the Queen. You get an immediate response to any '
                   'kind of question, and the system truly works. Thank you, '
                   'Bogdan! 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:29 # Thank you so much for the wonderful '
                   'feedback! I’m really happy to hear that Quantum Queen is '
                   'working well for you and that you feel supported — I’m '
                   'always here to help whenever you need. Wishing you '
                   'continued success and steady profits!',
 'review_datetime': '2025.05.29 17:57',
 'review_has_answer': False,
 'review_id': '38508546',
 'reviewer_id': 'm_schnitzer',
 'reviewer_name': 'Michael Schnitzer',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:54:57.061247',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:57 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '35 Steels is 2025.05.29 12:33 # "It is a great system and '
                   'a profitable, thank you Bogdan! 34750 Reply from developer '
                   'Bogdan Ion Puscasu 2025.06.05 14:28 # Thank you so much '
                   'for your kind words! I’m really glad to hear you’re seeing '
                   'great results with the system. That’s exactly what I aimed '
                   'for when building Quantum Queen. Your support means a lot '
                   '— thank you!',
 'review_datetime': '2025.05.29 12:33',
 'review_has_answer': False,
 'review_id': '38517999',
 'reviewer_id': 'steelis',
 'reviewer_name': 'Steels is',
 'reviewer_score': 35,
 'scraped_at': '2025-06-13T15:54:57.064090',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [review] INFO: Parsing reviews page 2 for product 118805
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Backtest results correlate very well with live '
                   'performance. Owner is super responsive and the Quantum '
                   'community is supportive and helpful as well. This has been '
                   'an excellent experience. Bought it on late May and profit '
                   'has already covered the cost of this EA (including VPS '
                   'cost). If you manage your risk and be patient, it just '
                   'pays itself off. This is as close you can get to an golden '
                   'goose.',
 'review_datetime': '2025.06.12 15:58',
 'review_has_answer': False,
 'review_id': '38658708',
 'reviewer_id': 'tiltedk',
 'reviewer_name': 'Thanos',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:54:58.228186',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 11:50',
 'review_has_answer': False,
 'review_id': '38766955',
 'reviewer_id': 'daryanahmmed',
 'reviewer_name': 'Daryan Ahmed',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:54:58.231522',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 04:27',
 'review_has_answer': False,
 'review_id': '38826636',
 'reviewer_id': 'chalermpetchara',
 'reviewer_name': 'chalerm petcharat',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:54:58.234064',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '599 Pitt Petruschke 2025.06.11 09:21 # ich nutze den EA '
                   'jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das '
                   'bedeutet, der macht wenige aber dafür profitabele Trades, '
                   'was meinem Trading Stil entgegenkommt. Der Support '
                   'arbeitet super schnell...',
 'review_datetime': '2025.06.11 09:21',
 'review_has_answer': False,
 'review_id': '38655413',
 'reviewer_id': 'pablo1904',
 'reviewer_name': 'Pitt Petruschke',
 'reviewer_score': 599,
 'scraped_at': '2025-06-13T15:54:58.236561',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I decided to leave a review after 2-3 weeks of testing on '
                   'a live account. I really like the way QQ5 works. Deals are '
                   'opened not very often, but qualitatively and I understand '
                   'that this is due to the fact that EA waits for the perfect '
                   'moment, taking into account the market situation. And when '
                   'it opens a deal, then another one and another and '
                   'eventually closes almost all deals with a profit. I have '
                   'never had a series of trades that went into the minus, '
                   'always in the plus. My settings are automatic with medium '
                   'risk. My broker is ByBit. I am very happy with the result '
                   "of QQ5's work, I will continue to trust him to manage my "
                   'budget. Thank you for such a quality EA! I recommend '
                   'everyone to buy it, the price matches the quality - and it '
                   'will definitely pay off!',
 'review_datetime': '2025.06.11 08:19',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'kirilka59',
 'reviewer_name': 'Kirilka59',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:54:58.238698',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '48 goateeeth maker 2025.06.08 02:25 # ⭐⭐⭐⭐⭐ Excellent '
                   "EA—Profitable and Reliable! I've been using this EA on MT5 "
                   "for several weeks, and it's exceeded my expectations. The "
                   'performance has been consistent, generating steady profits '
                   'while effectively managing risks. Setup was '
                   'straightforward, and customer support has been responsive '
                   'whenever I had questions. Highly recommend this EA to '
                   'anyone looking for an automated, stress-free trading '
                   'solution!',
 'review_datetime': '2025.06.08 02:25',
 'review_has_answer': False,
 'review_id': '38333720',
 'reviewer_id': 'goateeeth',
 'reviewer_name': 'goateeeth maker',
 'reviewer_score': 48,
 'scraped_at': '2025-06-13T15:54:58.242856',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '🌟 An Expert Advisor That Truly Delivers! 🌟 I’ve been using '
                   'Quantum Queen MT5 for a short time, but I’m already seeing '
                   'my account grow steadily. A colleague of mine has been '
                   'using it for over two months, and his results have been '
                   'exceptional and consistent, without hesitation. 🔹 It’s one '
                   'of the very few algobots on MetaTrader 5 that actually '
                   'works. 🔹 The developer is very professional and always '
                   'quick to respond to any questions. 🔹 It was recommended to '
                   'me by someone extremely meticulous… and it has exceeded '
                   'all expectations. 💰 It’s worth every cent of the price. '
                   'This is a professional tool, not just another empty '
                   'promise. 🔒 Reliable, profitable, and well-supported. '
                   'Highly recommended!',
 'review_datetime': '2025.06.06 23:02',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'saman83',
 'reviewer_name': 'Saman83',
 'reviewer_score': 79,
 'scraped_at': '2025-06-13T15:54:58.245234',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '53 SAMUEL DE SOUZA 2025.06.06 22:12 # Absolutely '
                   'Phenomenal – A True Queen in the Game! I’ve tested over 40 '
                   'different AEs in my trading journey, and I can confidently '
                   'say the Quantum Queen MT5 is in a league of its own. It’s '
                   'not just another gold (XAUUSD) AE—it’s a '
                   'precision-engineered powerhouse. From setup to execution, '
                   'everything runs smoothly and efficiently, adapting '
                   'perfectly to changing market conditions. Alongside The '
                   'Emperor, Quantum Queen is easily one of the two best AEs '
                   'I’ve ever come across. The consistency, reliability, and '
                   'intelligent trade management this bot brings are '
                   'unmatched. Whether you’re a seasoned trader or just '
                   'starting out, this is a must-have for your arsenal. Highly '
                   'recommend to anyone serious about mastering gold on MT5!',
 'review_datetime': '2025.06.06 22:12',
 'review_has_answer': False,
 'review_id': '38737428',
 'reviewer_id': 'swtsam2',
 'reviewer_name': 'SAMUEL DE SOUZA',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:54:58.247508',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '购买这个EA之后我基于最近半年短期做了很多次回测 根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 '
                   '每一次都是盈利的 而且都是非常可观的 用我们国家的一句老话来说就是 兵欲善其事 必先利其器 '
                   '我觉得QQ对我这个新手来说十分友好 并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的',
 'review_datetime': '2025.06.06 20:33',
 'review_has_answer': False,
 'review_id': '38705502',
 'reviewer_id': 'noalc',
 'reviewer_name': 'Noalc',
 'reviewer_score': 22,
 'scraped_at': '2025-06-13T15:54:58.249701',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "Bonjour je viens d'acheter expert Quantum Queen MT5 c'est "
                   "un très bon expert il est très stable il n'y a des perte "
                   'que très minime et donne plus de bon profit je le trouve '
                   'excellent. Très bon service après vente pour '
                   "l'installation et les conseils de Bogdan Ion Puscasu il a "
                   'répondu à mes demandes et à mes messages avec une rapidité '
                   'exemplaire je suis très satisfait merci beaucoup.',
 'review_datetime': '2025.06.06 02:24',
 'review_has_answer': False,
 'review_id': '38679597',
 'reviewer_id': 'dnamron',
 'reviewer_name': 'Dnamron',
 'reviewer_score': 66,
 'scraped_at': '2025-06-13T15:54:58.251814',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '"This is an EA that allows you to trade with great peace '
                   'of mind." 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:43 # Thank you so much! That’s exactly what '
                   'I wanted to achieve with Quantum Queen — an EA that brings '
                   'not just results, but also peace of mind. Trading '
                   'shouldn’t be stressful, and I’m really glad you’re feeling '
                   'that sense of calm while using it. If you ever have '
                   'questions or need anything, feel free to reach out '
                   'anytime!',
 'review_datetime': '2025.06.04 08:33',
 'review_has_answer': False,
 'review_id': '38661377',
 'reviewer_id': 'jza80spl',
 'reviewer_name': 'jza80spl',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:54:58.258456',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '378 Guorong He 2025.06.03 16:06 # Very good grid system. '
                   'Thanks Bogdan！ 34750 Reply from developer Bogdan Ion '
                   'Puscasu 2025.06.05 14:42 # Thank you so much for your '
                   "review! I’m really glad to hear you're enjoying the grid "
                   'system. It’s designed to be both smart and steady, and I’m '
                   'happy it’s working well for you. Appreciate the kind words '
                   '– and if you ever need help or want to explore more '
                   'Quantum EAs, I’m just a message away!',
 'review_datetime': '2025.06.03 16:06',
 'review_has_answer': False,
 'review_id': '38691977',
 'reviewer_id': 'tokio147',
 'reviewer_name': 'Guorong He',
 'reviewer_score': 378,
 'scraped_at': '2025-06-13T15:54:58.261344',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.02 21:18',
 'review_has_answer': False,
 'review_id': '38596644',
 'reviewer_id': 'molobamllr',
 'reviewer_name': 'Lorenz Goeminne',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:54:58.264678',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bought the EA last week and already made about 4% in just '
                   'one week! Really happy with the performance so far. The '
                   'documentation provided is very detailed and informative — '
                   'it made setup and understanding the strategy very easy. '
                   'Looking forward to seeing more great results. 34750 Reply '
                   'from developer Bogdan Ion Puscasu 2025.06.05 14:37 # '
                   "That's fantastic to hear! I'm really glad the setup "
                   "process went smoothly and that you're already seeing solid "
                   'results in just one week — 4% is a great start! Thanks for '
                   'pointing out the documentation too, I put a lot of effort '
                   'into making it as clear and helpful as possible. Wishing '
                   'you continued success, and if you ever need anything, '
                   'don’t hesitate to reach out.',
 'review_datetime': '2025.06.02 13:36',
 'review_has_answer': False,
 'review_id': '38600643',
 'reviewer_id': 'nasoo',
 'reviewer_name': 'Nasoo',
 'reviewer_score': 785,
 'scraped_at': '2025-06-13T15:54:58.267364',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I am very satisfied with the results from the 1st week, '
                   'its the same as in the backtests. I fully recommend it. '
                   '34750 Reply from developer Bogdan Ion Puscasu 2025.06.05 '
                   "14:35 # Thank you so much for the great feedback! I'm "
                   'really glad to hear that your first week with Quantum '
                   "Queen matches the backtests — that's always a great sign. "
                   "It means a lot to know you're satisfied and confident in "
                   'the EA. Wishing you continued success and steady profits '
                   "ahead! If you ever need help or have questions, I'm here.",
 'review_datetime': '2025.06.01 15:52',
 'review_has_answer': False,
 'review_id': '38580848',
 'reviewer_id': 'francesc00',
 'reviewer_name': 'Francesc00',
 'reviewer_score': 34,
 'scraped_at': '2025-06-13T15:54:58.281370',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': None,
 'rating_reliability': None,
 'rating_support': None,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.05.31 06:14',
 'review_has_answer': False,
 'review_id': '38668358',
 'reviewer_id': 'schnitzler88',
 'reviewer_name': 'Extoflex',
 'reviewer_score': 328,
 'scraped_at': '2025-06-13T15:54:58.287766',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '40 Lucas Ramos 2025.05.30 09:43 # O Bogdan é um Trader '
                   'extremamente responsável e simpático e oferece suporte '
                   'rápido, a Quantum Queen é uma ótima EA que já me rendeu '
                   'bons lucros com baixos riscos, recomendo fortemente o uso '
                   'dessa EA para Traders responsáveis e que queiram fazer seu '
                   'dinheiro render sem muito esforço 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:33 # Thank you '
                   "so much for your kind words and trust, Lucas! 🙏 I'm really "
                   'happy to hear that Quantum Queen is delivering good '
                   'profits with low risk for you — exactly what I designed it '
                   'for. I truly appreciate your support and recommendation, '
                   "and I'm always here if you need help or have any "
                   'questions. Wishing you continued success and steady growth '
                   "— let's keep making the most out of every trade!",
 'review_datetime': '2025.05.30 09:43',
 'review_has_answer': False,
 'review_id': '38537336',
 'reviewer_id': 'lucasramoss',
 'reviewer_name': 'Lucas Ramos',
 'reviewer_score': 40,
 'scraped_at': '2025-06-13T15:54:58.290479',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bogdan has been incredibly responsive, often replying '
                   'within minutes, which makes it feel like I have a tech '
                   'team right beside me — something I really appreciate. I’ve '
                   'been running the EA from May 26th to May 29th, and it '
                   'delivered a 2% profit within just four days using the '
                   'default risk settings. It doesn’t use a martingale '
                   'strategy, but it does attempt to average price levels in a '
                   'controlled manner. I especially like that it only executes '
                   'trades when specific market conditions are met, aiming for '
                   'higher probability setups. So far, the performance has '
                   'been solid and feels safe. Highly recommended, and based '
                   'on these results, I plan to explore more of their '
                   'products. 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:31 # Thank you so much for the thoughtful '
                   'and detailed feedback, Leo! 🙏 I’m really glad to hear that '
                   'you appreciate the way Quantum Queen handles trades — '
                   'focused, careful, and without martingale risk. I’ve spent '
                   'a lot of time fine-tuning the strategy to be both smart '
                   'and safe, and it’s great to know it’s performing well for '
                   'you. I’m also always happy to help, so don’t hesitate to '
                   'reach out anytime. Looking forward to having you explore '
                   'the rest of the Quantum family — there’s more good stuff '
                   'waiting!',
 'review_datetime': '2025.05.30 08:56',
 'review_has_answer': False,
 'review_id': '38237707',
 'reviewer_id': 'chasuhun23',
 'reviewer_name': 'Leo',
 'reviewer_score': 56,
 'scraped_at': '2025-06-13T15:54:58.293180',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '24 Michael Schnitzer 2025.05.29 17:57 # I can highly '
                   'recommend the Queen. You get an immediate response to any '
                   'kind of question, and the system truly works. Thank you, '
                   'Bogdan! 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:29 # Thank you so much for the wonderful '
                   'feedback! I’m really happy to hear that Quantum Queen is '
                   'working well for you and that you feel supported — I’m '
                   'always here to help whenever you need. Wishing you '
                   'continued success and steady profits!',
 'review_datetime': '2025.05.29 17:57',
 'review_has_answer': False,
 'review_id': '38508546',
 'reviewer_id': 'm_schnitzer',
 'reviewer_name': 'Michael Schnitzer',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:54:58.295835',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '35 Steels is 2025.05.29 12:33 # "It is a great system and '
                   'a profitable, thank you Bogdan! 34750 Reply from developer '
                   'Bogdan Ion Puscasu 2025.06.05 14:28 # Thank you so much '
                   'for your kind words! I’m really glad to hear you’re seeing '
                   'great results with the system. That’s exactly what I aimed '
                   'for when building Quantum Queen. Your support means a lot '
                   '— thank you!',
 'review_datetime': '2025.05.29 12:33',
 'review_has_answer': False,
 'review_id': '38517999',
 'reviewer_id': 'steelis',
 'reviewer_name': 'Steels is',
 'reviewer_score': 35,
 'scraped_at': '2025-06-13T15:54:58.299701',
 'timestamp_extraction_review': '2025-06-13 15:54',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:54:58 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:54:58 [review] INFO: Exported 1 items to output/review_20250613_155450.json
2025-06-13 15:54:58 [scrapy.extensions.feedexport] INFO: Stored json feed (1 items) in: review_improved.json
2025-06-13 15:54:58 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 5248,
 'downloader/request_count': 9,
 'downloader/request_method_count/GET': 9,
 'downloader/response_bytes': 665024,
 'downloader/response_count': 9,
 'downloader/response_status_count/200': 9,
 'elapsed_time_seconds': 7.695908,
 'feedexport/success_count/FileFeedStorage': 1,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 54, 58, 304456, tzinfo=datetime.timezone.utc),
 'httpcache/firsthand': 8,
 'httpcache/hit': 1,
 'httpcache/miss': 8,
 'httpcache/store': 8,
 'httpcompression/response_bytes': 2753245,
 'httpcompression/response_count': 9,
 'item_dropped_count': 159,
 'item_dropped_reasons_count/DropItem': 159,
 'item_scraped_count': 1,
 'items_per_minute': 8.571428571428571,
 'log_count/INFO': 20,
 'log_count/WARNING': 163,
 'memusage/max': 75837440,
 'memusage/startup': 75837440,
 'request_depth_max': 1,
 'response_received_count': 9,
 'responses_per_minute': 77.14285714285714,
 'scheduler/dequeued': 9,
 'scheduler/dequeued/memory': 9,
 'scheduler/enqueued': 9,
 'scheduler/enqueued/memory': 9,
 'start_time': datetime.datetime(2025, 6, 13, 13, 54, 50, 608548, tzinfo=datetime.timezone.utc)}
2025-06-13 15:54:58 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 15:59:44 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:59:44 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:59:44 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:59:44 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:59:44 [asyncio] DEBUG: Using selector: EpollSelector
2025-06-13 15:59:44 [scrapy.utils.log] DEBUG: Using reactor: twisted.internet.asyncioreactor.AsyncioSelectorReactor
2025-06-13 15:59:44 [scrapy.utils.log] DEBUG: Using asyncio event loop: asyncio.unix_events._UnixSelectorEventLoop
2025-06-13 15:59:44 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.feedexport.FeedExporter',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:59:44 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:59:44 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:59:44 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:59:44 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:59:44 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:59:44 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 15:59:44 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:59:44 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:59:44 [scrapy.extensions.httpcache] DEBUG: Using filesystem cache storage in /home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/.scrapy/httpcache
2025-06-13 15:59:44 [review] INFO: Spider opened: review
2025-06-13 15:59:44 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:59:44 [scrapy.core.engine] DEBUG: Crawled (200) <GET https://www.mql5.com/en/market/product/118805> (referer: None) ['cached']
2025-06-13 15:59:44 [review] INFO: Product 118805 has 153 reviews across 8 pages
2025-06-13 15:59:44 [scrapy.core.engine] DEBUG: Crawled (200) <GET https://www.mql5.com/en/market/product/118805#!tab=reviews> (referer: https://www.mql5.com/en/market/product/118805) ['cached']
2025-06-13 15:59:44 [scrapy.core.engine] DEBUG: Crawled (200) <GET https://www.mql5.com/en/market/product/118805#!tab=reviews&page=8> (referer: https://www.mql5.com/en/market/product/118805) ['cached']
2025-06-13 15:59:44 [scrapy.core.engine] DEBUG: Crawled (200) <GET https://www.mql5.com/en/market/product/118805#!tab=reviews&page=7> (referer: https://www.mql5.com/en/market/product/118805) ['cached']
2025-06-13 15:59:44 [scrapy.core.engine] DEBUG: Crawled (200) <GET https://www.mql5.com/en/market/product/118805#!tab=reviews&page=6> (referer: https://www.mql5.com/en/market/product/118805) ['cached']
2025-06-13 15:59:44 [scrapy.core.engine] DEBUG: Crawled (200) <GET https://www.mql5.com/en/market/product/118805#!tab=reviews&page=5> (referer: https://www.mql5.com/en/market/product/118805) ['cached']
2025-06-13 15:59:44 [scrapy.core.engine] DEBUG: Crawled (200) <GET https://www.mql5.com/en/market/product/118805#!tab=reviews&page=4> (referer: https://www.mql5.com/en/market/product/118805) ['cached']
2025-06-13 15:59:44 [scrapy.core.engine] DEBUG: Crawled (200) <GET https://www.mql5.com/en/market/product/118805#!tab=reviews&page=3> (referer: https://www.mql5.com/en/market/product/118805) ['cached']
2025-06-13 15:59:44 [scrapy.core.engine] DEBUG: Crawled (200) <GET https://www.mql5.com/en/market/product/118805#!tab=reviews&page=2> (referer: https://www.mql5.com/en/market/product/118805) ['cached']
2025-06-13 15:59:44 [review] INFO: Parsing reviews page 1 for product 118805
2025-06-13 15:59:44 [review] INFO: Parsing reviews page 8 for product 118805
2025-06-13 15:59:44 [review] INFO: Parsing reviews page 7 for product 118805
2025-06-13 15:59:44 [review] INFO: Parsing reviews page 6 for product 118805
2025-06-13 15:59:44 [review] INFO: Parsing reviews page 5 for product 118805
2025-06-13 15:59:44 [review] INFO: Parsing reviews page 4 for product 118805
2025-06-13 15:59:44 [review] INFO: Parsing reviews page 3 for product 118805
2025-06-13 15:59:44 [review] INFO: Parsing reviews page 2 for product 118805
2025-06-13 15:59:45 [scrapy.core.scraper] DEBUG: Scraped from <200 https://www.mql5.com/en/market/product/118805>
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Backtest results correlate very well with live '
                   'performance. Owner is super responsive and the Quantum '
                   'community is supportive and helpful as well. This has been '
                   'an excellent experience. Bought it on late May and profit '
                   'has already covered the cost of this EA (including VPS '
                   'cost). If you manage your risk and be patient, it just '
                   'pays itself off. This is as close you can get to an golden '
                   'goose.',
 'review_datetime': '2025.06.12 15:58',
 'review_has_answer': False,
 'review_id': '38658708',
 'reviewer_id': 'tiltedk',
 'reviewer_name': 'Thanos',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:59:44.857537',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Backtest results correlate very well with live '
                   'performance. Owner is super responsive and the Quantum '
                   'community is supportive and helpful as well. This has been '
                   'an excellent experience. Bought it on late May and profit '
                   'has already covered the cost of this EA (including VPS '
                   'cost). If you manage your risk and be patient, it just '
                   'pays itself off. This is as close you can get to an golden '
                   'goose.',
 'review_datetime': '2025.06.12 15:58',
 'review_has_answer': False,
 'review_id': '38658708',
 'reviewer_id': 'tiltedk',
 'reviewer_name': 'Thanos',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:59:44.877216',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Backtest results correlate very well with live '
                   'performance. Owner is super responsive and the Quantum '
                   'community is supportive and helpful as well. This has been '
                   'an excellent experience. Bought it on late May and profit '
                   'has already covered the cost of this EA (including VPS '
                   'cost). If you manage your risk and be patient, it just '
                   'pays itself off. This is as close you can get to an golden '
                   'goose.',
 'review_datetime': '2025.06.12 15:58',
 'review_has_answer': False,
 'review_id': '38658708',
 'reviewer_id': 'tiltedk',
 'reviewer_name': 'Thanos',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:59:44.899508',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Backtest results correlate very well with live '
                   'performance. Owner is super responsive and the Quantum '
                   'community is supportive and helpful as well. This has been '
                   'an excellent experience. Bought it on late May and profit '
                   'has already covered the cost of this EA (including VPS '
                   'cost). If you manage your risk and be patient, it just '
                   'pays itself off. This is as close you can get to an golden '
                   'goose.',
 'review_datetime': '2025.06.12 15:58',
 'review_has_answer': False,
 'review_id': '38658708',
 'reviewer_id': 'tiltedk',
 'reviewer_name': 'Thanos',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:59:44.916924',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Backtest results correlate very well with live '
                   'performance. Owner is super responsive and the Quantum '
                   'community is supportive and helpful as well. This has been '
                   'an excellent experience. Bought it on late May and profit '
                   'has already covered the cost of this EA (including VPS '
                   'cost). If you manage your risk and be patient, it just '
                   'pays itself off. This is as close you can get to an golden '
                   'goose.',
 'review_datetime': '2025.06.12 15:58',
 'review_has_answer': False,
 'review_id': '38658708',
 'reviewer_id': 'tiltedk',
 'reviewer_name': 'Thanos',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:59:44.934086',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Backtest results correlate very well with live '
                   'performance. Owner is super responsive and the Quantum '
                   'community is supportive and helpful as well. This has been '
                   'an excellent experience. Bought it on late May and profit '
                   'has already covered the cost of this EA (including VPS '
                   'cost). If you manage your risk and be patient, it just '
                   'pays itself off. This is as close you can get to an golden '
                   'goose.',
 'review_datetime': '2025.06.12 15:58',
 'review_has_answer': False,
 'review_id': '38658708',
 'reviewer_id': 'tiltedk',
 'reviewer_name': 'Thanos',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:59:44.950441',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Backtest results correlate very well with live '
                   'performance. Owner is super responsive and the Quantum '
                   'community is supportive and helpful as well. This has been '
                   'an excellent experience. Bought it on late May and profit '
                   'has already covered the cost of this EA (including VPS '
                   'cost). If you manage your risk and be patient, it just '
                   'pays itself off. This is as close you can get to an golden '
                   'goose.',
 'review_datetime': '2025.06.12 15:58',
 'review_has_answer': False,
 'review_id': '38658708',
 'reviewer_id': 'tiltedk',
 'reviewer_name': 'Thanos',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:59:44.965978',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Backtest results correlate very well with live '
                   'performance. Owner is super responsive and the Quantum '
                   'community is supportive and helpful as well. This has been '
                   'an excellent experience. Bought it on late May and profit '
                   'has already covered the cost of this EA (including VPS '
                   'cost). If you manage your risk and be patient, it just '
                   'pays itself off. This is as close you can get to an golden '
                   'goose.',
 'review_datetime': '2025.06.12 15:58',
 'review_has_answer': False,
 'review_id': '38658708',
 'reviewer_id': 'tiltedk',
 'reviewer_name': 'Thanos',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:59:44.984051',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 11:50',
 'review_has_answer': False,
 'review_id': '38766955',
 'reviewer_id': 'daryanahmmed',
 'reviewer_name': 'Daryan Ahmed',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:59:44.987644',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 04:27',
 'review_has_answer': False,
 'review_id': '38826636',
 'reviewer_id': 'chalermpetchara',
 'reviewer_name': 'chalerm petcharat',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:59:44.989888',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '599 Pitt Petruschke 2025.06.11 09:21 # ich nutze den EA '
                   'jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das '
                   'bedeutet, der macht wenige aber dafür profitabele Trades, '
                   'was meinem Trading Stil entgegenkommt. Der Support '
                   'arbeitet super schnell...',
 'review_datetime': '2025.06.11 09:21',
 'review_has_answer': False,
 'review_id': '38655413',
 'reviewer_id': 'pablo1904',
 'reviewer_name': 'Pitt Petruschke',
 'reviewer_score': 599,
 'scraped_at': '2025-06-13T15:59:44.992186',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I decided to leave a review after 2-3 weeks of testing on '
                   'a live account. I really like the way QQ5 works. Deals are '
                   'opened not very often, but qualitatively and I understand '
                   'that this is due to the fact that EA waits for the perfect '
                   'moment, taking into account the market situation. And when '
                   'it opens a deal, then another one and another and '
                   'eventually closes almost all deals with a profit. I have '
                   'never had a series of trades that went into the minus, '
                   'always in the plus. My settings are automatic with medium '
                   'risk. My broker is ByBit. I am very happy with the result '
                   "of QQ5's work, I will continue to trust him to manage my "
                   'budget. Thank you for such a quality EA! I recommend '
                   'everyone to buy it, the price matches the quality - and it '
                   'will definitely pay off!',
 'review_datetime': '2025.06.11 08:19',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'kirilka59',
 'reviewer_name': 'Kirilka59',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:59:44.994290',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '48 goateeeth maker 2025.06.08 02:25 # ⭐⭐⭐⭐⭐ Excellent '
                   "EA—Profitable and Reliable! I've been using this EA on MT5 "
                   "for several weeks, and it's exceeded my expectations. The "
                   'performance has been consistent, generating steady profits '
                   'while effectively managing risks. Setup was '
                   'straightforward, and customer support has been responsive '
                   'whenever I had questions. Highly recommend this EA to '
                   'anyone looking for an automated, stress-free trading '
                   'solution!',
 'review_datetime': '2025.06.08 02:25',
 'review_has_answer': False,
 'review_id': '38333720',
 'reviewer_id': 'goateeeth',
 'reviewer_name': 'goateeeth maker',
 'reviewer_score': 48,
 'scraped_at': '2025-06-13T15:59:44.996266',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 11:50',
 'review_has_answer': False,
 'review_id': '38766955',
 'reviewer_id': 'daryanahmmed',
 'reviewer_name': 'Daryan Ahmed',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:59:44.999680',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 04:27',
 'review_has_answer': False,
 'review_id': '38826636',
 'reviewer_id': 'chalermpetchara',
 'reviewer_name': 'chalerm petcharat',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:59:45.002798',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '599 Pitt Petruschke 2025.06.11 09:21 # ich nutze den EA '
                   'jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das '
                   'bedeutet, der macht wenige aber dafür profitabele Trades, '
                   'was meinem Trading Stil entgegenkommt. Der Support '
                   'arbeitet super schnell...',
 'review_datetime': '2025.06.11 09:21',
 'review_has_answer': False,
 'review_id': '38655413',
 'reviewer_id': 'pablo1904',
 'reviewer_name': 'Pitt Petruschke',
 'reviewer_score': 599,
 'scraped_at': '2025-06-13T15:59:45.005019',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I decided to leave a review after 2-3 weeks of testing on '
                   'a live account. I really like the way QQ5 works. Deals are '
                   'opened not very often, but qualitatively and I understand '
                   'that this is due to the fact that EA waits for the perfect '
                   'moment, taking into account the market situation. And when '
                   'it opens a deal, then another one and another and '
                   'eventually closes almost all deals with a profit. I have '
                   'never had a series of trades that went into the minus, '
                   'always in the plus. My settings are automatic with medium '
                   'risk. My broker is ByBit. I am very happy with the result '
                   "of QQ5's work, I will continue to trust him to manage my "
                   'budget. Thank you for such a quality EA! I recommend '
                   'everyone to buy it, the price matches the quality - and it '
                   'will definitely pay off!',
 'review_datetime': '2025.06.11 08:19',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'kirilka59',
 'reviewer_name': 'Kirilka59',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:59:45.007138',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 11:50',
 'review_has_answer': False,
 'review_id': '38766955',
 'reviewer_id': 'daryanahmmed',
 'reviewer_name': 'Daryan Ahmed',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:59:45.010251',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 04:27',
 'review_has_answer': False,
 'review_id': '38826636',
 'reviewer_id': 'chalermpetchara',
 'reviewer_name': 'chalerm petcharat',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:59:45.012945',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '599 Pitt Petruschke 2025.06.11 09:21 # ich nutze den EA '
                   'jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das '
                   'bedeutet, der macht wenige aber dafür profitabele Trades, '
                   'was meinem Trading Stil entgegenkommt. Der Support '
                   'arbeitet super schnell...',
 'review_datetime': '2025.06.11 09:21',
 'review_has_answer': False,
 'review_id': '38655413',
 'reviewer_id': 'pablo1904',
 'reviewer_name': 'Pitt Petruschke',
 'reviewer_score': 599,
 'scraped_at': '2025-06-13T15:59:45.015001',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I decided to leave a review after 2-3 weeks of testing on '
                   'a live account. I really like the way QQ5 works. Deals are '
                   'opened not very often, but qualitatively and I understand '
                   'that this is due to the fact that EA waits for the perfect '
                   'moment, taking into account the market situation. And when '
                   'it opens a deal, then another one and another and '
                   'eventually closes almost all deals with a profit. I have '
                   'never had a series of trades that went into the minus, '
                   'always in the plus. My settings are automatic with medium '
                   'risk. My broker is ByBit. I am very happy with the result '
                   "of QQ5's work, I will continue to trust him to manage my "
                   'budget. Thank you for such a quality EA! I recommend '
                   'everyone to buy it, the price matches the quality - and it '
                   'will definitely pay off!',
 'review_datetime': '2025.06.11 08:19',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'kirilka59',
 'reviewer_name': 'Kirilka59',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:59:45.017342',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 11:50',
 'review_has_answer': False,
 'review_id': '38766955',
 'reviewer_id': 'daryanahmmed',
 'reviewer_name': 'Daryan Ahmed',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:59:45.019834',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 04:27',
 'review_has_answer': False,
 'review_id': '38826636',
 'reviewer_id': 'chalermpetchara',
 'reviewer_name': 'chalerm petcharat',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:59:45.022622',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '599 Pitt Petruschke 2025.06.11 09:21 # ich nutze den EA '
                   'jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das '
                   'bedeutet, der macht wenige aber dafür profitabele Trades, '
                   'was meinem Trading Stil entgegenkommt. Der Support '
                   'arbeitet super schnell...',
 'review_datetime': '2025.06.11 09:21',
 'review_has_answer': False,
 'review_id': '38655413',
 'reviewer_id': 'pablo1904',
 'reviewer_name': 'Pitt Petruschke',
 'reviewer_score': 599,
 'scraped_at': '2025-06-13T15:59:45.024893',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I decided to leave a review after 2-3 weeks of testing on '
                   'a live account. I really like the way QQ5 works. Deals are '
                   'opened not very often, but qualitatively and I understand '
                   'that this is due to the fact that EA waits for the perfect '
                   'moment, taking into account the market situation. And when '
                   'it opens a deal, then another one and another and '
                   'eventually closes almost all deals with a profit. I have '
                   'never had a series of trades that went into the minus, '
                   'always in the plus. My settings are automatic with medium '
                   'risk. My broker is ByBit. I am very happy with the result '
                   "of QQ5's work, I will continue to trust him to manage my "
                   'budget. Thank you for such a quality EA! I recommend '
                   'everyone to buy it, the price matches the quality - and it '
                   'will definitely pay off!',
 'review_datetime': '2025.06.11 08:19',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'kirilka59',
 'reviewer_name': 'Kirilka59',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:59:45.027275',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '48 goateeeth maker 2025.06.08 02:25 # ⭐⭐⭐⭐⭐ Excellent '
                   "EA—Profitable and Reliable! I've been using this EA on MT5 "
                   "for several weeks, and it's exceeded my expectations. The "
                   'performance has been consistent, generating steady profits '
                   'while effectively managing risks. Setup was '
                   'straightforward, and customer support has been responsive '
                   'whenever I had questions. Highly recommend this EA to '
                   'anyone looking for an automated, stress-free trading '
                   'solution!',
 'review_datetime': '2025.06.08 02:25',
 'review_has_answer': False,
 'review_id': '38333720',
 'reviewer_id': 'goateeeth',
 'reviewer_name': 'goateeeth maker',
 'reviewer_score': 48,
 'scraped_at': '2025-06-13T15:59:45.029665',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 11:50',
 'review_has_answer': False,
 'review_id': '38766955',
 'reviewer_id': 'daryanahmmed',
 'reviewer_name': 'Daryan Ahmed',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:59:45.032317',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 04:27',
 'review_has_answer': False,
 'review_id': '38826636',
 'reviewer_id': 'chalermpetchara',
 'reviewer_name': 'chalerm petcharat',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:59:45.035451',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '599 Pitt Petruschke 2025.06.11 09:21 # ich nutze den EA '
                   'jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das '
                   'bedeutet, der macht wenige aber dafür profitabele Trades, '
                   'was meinem Trading Stil entgegenkommt. Der Support '
                   'arbeitet super schnell...',
 'review_datetime': '2025.06.11 09:21',
 'review_has_answer': False,
 'review_id': '38655413',
 'reviewer_id': 'pablo1904',
 'reviewer_name': 'Pitt Petruschke',
 'reviewer_score': 599,
 'scraped_at': '2025-06-13T15:59:45.037430',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I decided to leave a review after 2-3 weeks of testing on '
                   'a live account. I really like the way QQ5 works. Deals are '
                   'opened not very often, but qualitatively and I understand '
                   'that this is due to the fact that EA waits for the perfect '
                   'moment, taking into account the market situation. And when '
                   'it opens a deal, then another one and another and '
                   'eventually closes almost all deals with a profit. I have '
                   'never had a series of trades that went into the minus, '
                   'always in the plus. My settings are automatic with medium '
                   'risk. My broker is ByBit. I am very happy with the result '
                   "of QQ5's work, I will continue to trust him to manage my "
                   'budget. Thank you for such a quality EA! I recommend '
                   'everyone to buy it, the price matches the quality - and it '
                   'will definitely pay off!',
 'review_datetime': '2025.06.11 08:19',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'kirilka59',
 'reviewer_name': 'Kirilka59',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:59:45.039465',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '48 goateeeth maker 2025.06.08 02:25 # ⭐⭐⭐⭐⭐ Excellent '
                   "EA—Profitable and Reliable! I've been using this EA on MT5 "
                   "for several weeks, and it's exceeded my expectations. The "
                   'performance has been consistent, generating steady profits '
                   'while effectively managing risks. Setup was '
                   'straightforward, and customer support has been responsive '
                   'whenever I had questions. Highly recommend this EA to '
                   'anyone looking for an automated, stress-free trading '
                   'solution!',
 'review_datetime': '2025.06.08 02:25',
 'review_has_answer': False,
 'review_id': '38333720',
 'reviewer_id': 'goateeeth',
 'reviewer_name': 'goateeeth maker',
 'reviewer_score': 48,
 'scraped_at': '2025-06-13T15:59:45.041651',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 11:50',
 'review_has_answer': False,
 'review_id': '38766955',
 'reviewer_id': 'daryanahmmed',
 'reviewer_name': 'Daryan Ahmed',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:59:45.044159',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 04:27',
 'review_has_answer': False,
 'review_id': '38826636',
 'reviewer_id': 'chalermpetchara',
 'reviewer_name': 'chalerm petcharat',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:59:45.047050',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '599 Pitt Petruschke 2025.06.11 09:21 # ich nutze den EA '
                   'jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das '
                   'bedeutet, der macht wenige aber dafür profitabele Trades, '
                   'was meinem Trading Stil entgegenkommt. Der Support '
                   'arbeitet super schnell...',
 'review_datetime': '2025.06.11 09:21',
 'review_has_answer': False,
 'review_id': '38655413',
 'reviewer_id': 'pablo1904',
 'reviewer_name': 'Pitt Petruschke',
 'reviewer_score': 599,
 'scraped_at': '2025-06-13T15:59:45.049024',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I decided to leave a review after 2-3 weeks of testing on '
                   'a live account. I really like the way QQ5 works. Deals are '
                   'opened not very often, but qualitatively and I understand '
                   'that this is due to the fact that EA waits for the perfect '
                   'moment, taking into account the market situation. And when '
                   'it opens a deal, then another one and another and '
                   'eventually closes almost all deals with a profit. I have '
                   'never had a series of trades that went into the minus, '
                   'always in the plus. My settings are automatic with medium '
                   'risk. My broker is ByBit. I am very happy with the result '
                   "of QQ5's work, I will continue to trust him to manage my "
                   'budget. Thank you for such a quality EA! I recommend '
                   'everyone to buy it, the price matches the quality - and it '
                   'will definitely pay off!',
 'review_datetime': '2025.06.11 08:19',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'kirilka59',
 'reviewer_name': 'Kirilka59',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:59:45.051011',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '48 goateeeth maker 2025.06.08 02:25 # ⭐⭐⭐⭐⭐ Excellent '
                   "EA—Profitable and Reliable! I've been using this EA on MT5 "
                   "for several weeks, and it's exceeded my expectations. The "
                   'performance has been consistent, generating steady profits '
                   'while effectively managing risks. Setup was '
                   'straightforward, and customer support has been responsive '
                   'whenever I had questions. Highly recommend this EA to '
                   'anyone looking for an automated, stress-free trading '
                   'solution!',
 'review_datetime': '2025.06.08 02:25',
 'review_has_answer': False,
 'review_id': '38333720',
 'reviewer_id': 'goateeeth',
 'reviewer_name': 'goateeeth maker',
 'reviewer_score': 48,
 'scraped_at': '2025-06-13T15:59:45.053228',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 11:50',
 'review_has_answer': False,
 'review_id': '38766955',
 'reviewer_id': 'daryanahmmed',
 'reviewer_name': 'Daryan Ahmed',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:59:45.055799',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 04:27',
 'review_has_answer': False,
 'review_id': '38826636',
 'reviewer_id': 'chalermpetchara',
 'reviewer_name': 'chalerm petcharat',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:59:45.058486',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '599 Pitt Petruschke 2025.06.11 09:21 # ich nutze den EA '
                   'jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das '
                   'bedeutet, der macht wenige aber dafür profitabele Trades, '
                   'was meinem Trading Stil entgegenkommt. Der Support '
                   'arbeitet super schnell...',
 'review_datetime': '2025.06.11 09:21',
 'review_has_answer': False,
 'review_id': '38655413',
 'reviewer_id': 'pablo1904',
 'reviewer_name': 'Pitt Petruschke',
 'reviewer_score': 599,
 'scraped_at': '2025-06-13T15:59:45.060459',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I decided to leave a review after 2-3 weeks of testing on '
                   'a live account. I really like the way QQ5 works. Deals are '
                   'opened not very often, but qualitatively and I understand '
                   'that this is due to the fact that EA waits for the perfect '
                   'moment, taking into account the market situation. And when '
                   'it opens a deal, then another one and another and '
                   'eventually closes almost all deals with a profit. I have '
                   'never had a series of trades that went into the minus, '
                   'always in the plus. My settings are automatic with medium '
                   'risk. My broker is ByBit. I am very happy with the result '
                   "of QQ5's work, I will continue to trust him to manage my "
                   'budget. Thank you for such a quality EA! I recommend '
                   'everyone to buy it, the price matches the quality - and it '
                   'will definitely pay off!',
 'review_datetime': '2025.06.11 08:19',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'kirilka59',
 'reviewer_name': 'Kirilka59',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:59:45.062645',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '48 goateeeth maker 2025.06.08 02:25 # ⭐⭐⭐⭐⭐ Excellent '
                   "EA—Profitable and Reliable! I've been using this EA on MT5 "
                   "for several weeks, and it's exceeded my expectations. The "
                   'performance has been consistent, generating steady profits '
                   'while effectively managing risks. Setup was '
                   'straightforward, and customer support has been responsive '
                   'whenever I had questions. Highly recommend this EA to '
                   'anyone looking for an automated, stress-free trading '
                   'solution!',
 'review_datetime': '2025.06.08 02:25',
 'review_has_answer': False,
 'review_id': '38333720',
 'reviewer_id': 'goateeeth',
 'reviewer_name': 'goateeeth maker',
 'reviewer_score': 48,
 'scraped_at': '2025-06-13T15:59:45.065249',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 11:50',
 'review_has_answer': False,
 'review_id': '38766955',
 'reviewer_id': 'daryanahmmed',
 'reviewer_name': 'Daryan Ahmed',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:59:45.068154',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.12 04:27',
 'review_has_answer': False,
 'review_id': '38826636',
 'reviewer_id': 'chalermpetchara',
 'reviewer_name': 'chalerm petcharat',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:59:45.071312',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '599 Pitt Petruschke 2025.06.11 09:21 # ich nutze den EA '
                   'jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das '
                   'bedeutet, der macht wenige aber dafür profitabele Trades, '
                   'was meinem Trading Stil entgegenkommt. Der Support '
                   'arbeitet super schnell...',
 'review_datetime': '2025.06.11 09:21',
 'review_has_answer': False,
 'review_id': '38655413',
 'reviewer_id': 'pablo1904',
 'reviewer_name': 'Pitt Petruschke',
 'reviewer_score': 599,
 'scraped_at': '2025-06-13T15:59:45.073637',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I decided to leave a review after 2-3 weeks of testing on '
                   'a live account. I really like the way QQ5 works. Deals are '
                   'opened not very often, but qualitatively and I understand '
                   'that this is due to the fact that EA waits for the perfect '
                   'moment, taking into account the market situation. And when '
                   'it opens a deal, then another one and another and '
                   'eventually closes almost all deals with a profit. I have '
                   'never had a series of trades that went into the minus, '
                   'always in the plus. My settings are automatic with medium '
                   'risk. My broker is ByBit. I am very happy with the result '
                   "of QQ5's work, I will continue to trust him to manage my "
                   'budget. Thank you for such a quality EA! I recommend '
                   'everyone to buy it, the price matches the quality - and it '
                   'will definitely pay off!',
 'review_datetime': '2025.06.11 08:19',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'kirilka59',
 'reviewer_name': 'Kirilka59',
 'reviewer_score': 36,
 'scraped_at': '2025-06-13T15:59:45.076078',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '🌟 An Expert Advisor That Truly Delivers! 🌟 I’ve been using '
                   'Quantum Queen MT5 for a short time, but I’m already seeing '
                   'my account grow steadily. A colleague of mine has been '
                   'using it for over two months, and his results have been '
                   'exceptional and consistent, without hesitation. 🔹 It’s one '
                   'of the very few algobots on MetaTrader 5 that actually '
                   'works. 🔹 The developer is very professional and always '
                   'quick to respond to any questions. 🔹 It was recommended to '
                   'me by someone extremely meticulous… and it has exceeded '
                   'all expectations. 💰 It’s worth every cent of the price. '
                   'This is a professional tool, not just another empty '
                   'promise. 🔒 Reliable, profitable, and well-supported. '
                   'Highly recommended!',
 'review_datetime': '2025.06.06 23:02',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'saman83',
 'reviewer_name': 'Saman83',
 'reviewer_score': 79,
 'scraped_at': '2025-06-13T15:59:45.085846',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '53 SAMUEL DE SOUZA 2025.06.06 22:12 # Absolutely '
                   'Phenomenal – A True Queen in the Game! I’ve tested over 40 '
                   'different AEs in my trading journey, and I can confidently '
                   'say the Quantum Queen MT5 is in a league of its own. It’s '
                   'not just another gold (XAUUSD) AE—it’s a '
                   'precision-engineered powerhouse. From setup to execution, '
                   'everything runs smoothly and efficiently, adapting '
                   'perfectly to changing market conditions. Alongside The '
                   'Emperor, Quantum Queen is easily one of the two best AEs '
                   'I’ve ever come across. The consistency, reliability, and '
                   'intelligent trade management this bot brings are '
                   'unmatched. Whether you’re a seasoned trader or just '
                   'starting out, this is a must-have for your arsenal. Highly '
                   'recommend to anyone serious about mastering gold on MT5!',
 'review_datetime': '2025.06.06 22:12',
 'review_has_answer': False,
 'review_id': '38737428',
 'reviewer_id': 'swtsam2',
 'reviewer_name': 'SAMUEL DE SOUZA',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:59:45.088587',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '购买这个EA之后我基于最近半年短期做了很多次回测 根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 '
                   '每一次都是盈利的 而且都是非常可观的 用我们国家的一句老话来说就是 兵欲善其事 必先利其器 '
                   '我觉得QQ对我这个新手来说十分友好 并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的',
 'review_datetime': '2025.06.06 20:33',
 'review_has_answer': False,
 'review_id': '38705502',
 'reviewer_id': 'noalc',
 'reviewer_name': 'Noalc',
 'reviewer_score': 22,
 'scraped_at': '2025-06-13T15:59:45.090981',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "Bonjour je viens d'acheter expert Quantum Queen MT5 c'est "
                   "un très bon expert il est très stable il n'y a des perte "
                   'que très minime et donne plus de bon profit je le trouve '
                   'excellent. Très bon service après vente pour '
                   "l'installation et les conseils de Bogdan Ion Puscasu il a "
                   'répondu à mes demandes et à mes messages avec une rapidité '
                   'exemplaire je suis très satisfait merci beaucoup.',
 'review_datetime': '2025.06.06 02:24',
 'review_has_answer': False,
 'review_id': '38679597',
 'reviewer_id': 'dnamron',
 'reviewer_name': 'Dnamron',
 'reviewer_score': 66,
 'scraped_at': '2025-06-13T15:59:45.093489',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '48 goateeeth maker 2025.06.08 02:25 # ⭐⭐⭐⭐⭐ Excellent '
                   "EA—Profitable and Reliable! I've been using this EA on MT5 "
                   "for several weeks, and it's exceeded my expectations. The "
                   'performance has been consistent, generating steady profits '
                   'while effectively managing risks. Setup was '
                   'straightforward, and customer support has been responsive '
                   'whenever I had questions. Highly recommend this EA to '
                   'anyone looking for an automated, stress-free trading '
                   'solution!',
 'review_datetime': '2025.06.08 02:25',
 'review_has_answer': False,
 'review_id': '38333720',
 'reviewer_id': 'goateeeth',
 'reviewer_name': 'goateeeth maker',
 'reviewer_score': 48,
 'scraped_at': '2025-06-13T15:59:45.096787',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '🌟 An Expert Advisor That Truly Delivers! 🌟 I’ve been using '
                   'Quantum Queen MT5 for a short time, but I’m already seeing '
                   'my account grow steadily. A colleague of mine has been '
                   'using it for over two months, and his results have been '
                   'exceptional and consistent, without hesitation. 🔹 It’s one '
                   'of the very few algobots on MetaTrader 5 that actually '
                   'works. 🔹 The developer is very professional and always '
                   'quick to respond to any questions. 🔹 It was recommended to '
                   'me by someone extremely meticulous… and it has exceeded '
                   'all expectations. 💰 It’s worth every cent of the price. '
                   'This is a professional tool, not just another empty '
                   'promise. 🔒 Reliable, profitable, and well-supported. '
                   'Highly recommended!',
 'review_datetime': '2025.06.06 23:02',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'saman83',
 'reviewer_name': 'Saman83',
 'reviewer_score': 79,
 'scraped_at': '2025-06-13T15:59:45.099438',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '53 SAMUEL DE SOUZA 2025.06.06 22:12 # Absolutely '
                   'Phenomenal – A True Queen in the Game! I’ve tested over 40 '
                   'different AEs in my trading journey, and I can confidently '
                   'say the Quantum Queen MT5 is in a league of its own. It’s '
                   'not just another gold (XAUUSD) AE—it’s a '
                   'precision-engineered powerhouse. From setup to execution, '
                   'everything runs smoothly and efficiently, adapting '
                   'perfectly to changing market conditions. Alongside The '
                   'Emperor, Quantum Queen is easily one of the two best AEs '
                   'I’ve ever come across. The consistency, reliability, and '
                   'intelligent trade management this bot brings are '
                   'unmatched. Whether you’re a seasoned trader or just '
                   'starting out, this is a must-have for your arsenal. Highly '
                   'recommend to anyone serious about mastering gold on MT5!',
 'review_datetime': '2025.06.06 22:12',
 'review_has_answer': False,
 'review_id': '38737428',
 'reviewer_id': 'swtsam2',
 'reviewer_name': 'SAMUEL DE SOUZA',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:59:45.102415',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '购买这个EA之后我基于最近半年短期做了很多次回测 根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 '
                   '每一次都是盈利的 而且都是非常可观的 用我们国家的一句老话来说就是 兵欲善其事 必先利其器 '
                   '我觉得QQ对我这个新手来说十分友好 并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的',
 'review_datetime': '2025.06.06 20:33',
 'review_has_answer': False,
 'review_id': '38705502',
 'reviewer_id': 'noalc',
 'reviewer_name': 'Noalc',
 'reviewer_score': 22,
 'scraped_at': '2025-06-13T15:59:45.104779',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '48 goateeeth maker 2025.06.08 02:25 # ⭐⭐⭐⭐⭐ Excellent '
                   "EA—Profitable and Reliable! I've been using this EA on MT5 "
                   "for several weeks, and it's exceeded my expectations. The "
                   'performance has been consistent, generating steady profits '
                   'while effectively managing risks. Setup was '
                   'straightforward, and customer support has been responsive '
                   'whenever I had questions. Highly recommend this EA to '
                   'anyone looking for an automated, stress-free trading '
                   'solution!',
 'review_datetime': '2025.06.08 02:25',
 'review_has_answer': False,
 'review_id': '38333720',
 'reviewer_id': 'goateeeth',
 'reviewer_name': 'goateeeth maker',
 'reviewer_score': 48,
 'scraped_at': '2025-06-13T15:59:45.107702',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '🌟 An Expert Advisor That Truly Delivers! 🌟 I’ve been using '
                   'Quantum Queen MT5 for a short time, but I’m already seeing '
                   'my account grow steadily. A colleague of mine has been '
                   'using it for over two months, and his results have been '
                   'exceptional and consistent, without hesitation. 🔹 It’s one '
                   'of the very few algobots on MetaTrader 5 that actually '
                   'works. 🔹 The developer is very professional and always '
                   'quick to respond to any questions. 🔹 It was recommended to '
                   'me by someone extremely meticulous… and it has exceeded '
                   'all expectations. 💰 It’s worth every cent of the price. '
                   'This is a professional tool, not just another empty '
                   'promise. 🔒 Reliable, profitable, and well-supported. '
                   'Highly recommended!',
 'review_datetime': '2025.06.06 23:02',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'saman83',
 'reviewer_name': 'Saman83',
 'reviewer_score': 79,
 'scraped_at': '2025-06-13T15:59:45.110625',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '53 SAMUEL DE SOUZA 2025.06.06 22:12 # Absolutely '
                   'Phenomenal – A True Queen in the Game! I’ve tested over 40 '
                   'different AEs in my trading journey, and I can confidently '
                   'say the Quantum Queen MT5 is in a league of its own. It’s '
                   'not just another gold (XAUUSD) AE—it’s a '
                   'precision-engineered powerhouse. From setup to execution, '
                   'everything runs smoothly and efficiently, adapting '
                   'perfectly to changing market conditions. Alongside The '
                   'Emperor, Quantum Queen is easily one of the two best AEs '
                   'I’ve ever come across. The consistency, reliability, and '
                   'intelligent trade management this bot brings are '
                   'unmatched. Whether you’re a seasoned trader or just '
                   'starting out, this is a must-have for your arsenal. Highly '
                   'recommend to anyone serious about mastering gold on MT5!',
 'review_datetime': '2025.06.06 22:12',
 'review_has_answer': False,
 'review_id': '38737428',
 'reviewer_id': 'swtsam2',
 'reviewer_name': 'SAMUEL DE SOUZA',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:59:45.113109',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '购买这个EA之后我基于最近半年短期做了很多次回测 根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 '
                   '每一次都是盈利的 而且都是非常可观的 用我们国家的一句老话来说就是 兵欲善其事 必先利其器 '
                   '我觉得QQ对我这个新手来说十分友好 并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的',
 'review_datetime': '2025.06.06 20:33',
 'review_has_answer': False,
 'review_id': '38705502',
 'reviewer_id': 'noalc',
 'reviewer_name': 'Noalc',
 'reviewer_score': 22,
 'scraped_at': '2025-06-13T15:59:45.115359',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '🌟 An Expert Advisor That Truly Delivers! 🌟 I’ve been using '
                   'Quantum Queen MT5 for a short time, but I’m already seeing '
                   'my account grow steadily. A colleague of mine has been '
                   'using it for over two months, and his results have been '
                   'exceptional and consistent, without hesitation. 🔹 It’s one '
                   'of the very few algobots on MetaTrader 5 that actually '
                   'works. 🔹 The developer is very professional and always '
                   'quick to respond to any questions. 🔹 It was recommended to '
                   'me by someone extremely meticulous… and it has exceeded '
                   'all expectations. 💰 It’s worth every cent of the price. '
                   'This is a professional tool, not just another empty '
                   'promise. 🔒 Reliable, profitable, and well-supported. '
                   'Highly recommended!',
 'review_datetime': '2025.06.06 23:02',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'saman83',
 'reviewer_name': 'Saman83',
 'reviewer_score': 79,
 'scraped_at': '2025-06-13T15:59:45.117929',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '53 SAMUEL DE SOUZA 2025.06.06 22:12 # Absolutely '
                   'Phenomenal – A True Queen in the Game! I’ve tested over 40 '
                   'different AEs in my trading journey, and I can confidently '
                   'say the Quantum Queen MT5 is in a league of its own. It’s '
                   'not just another gold (XAUUSD) AE—it’s a '
                   'precision-engineered powerhouse. From setup to execution, '
                   'everything runs smoothly and efficiently, adapting '
                   'perfectly to changing market conditions. Alongside The '
                   'Emperor, Quantum Queen is easily one of the two best AEs '
                   'I’ve ever come across. The consistency, reliability, and '
                   'intelligent trade management this bot brings are '
                   'unmatched. Whether you’re a seasoned trader or just '
                   'starting out, this is a must-have for your arsenal. Highly '
                   'recommend to anyone serious about mastering gold on MT5!',
 'review_datetime': '2025.06.06 22:12',
 'review_has_answer': False,
 'review_id': '38737428',
 'reviewer_id': 'swtsam2',
 'reviewer_name': 'SAMUEL DE SOUZA',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:59:45.120278',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '购买这个EA之后我基于最近半年短期做了很多次回测 根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 '
                   '每一次都是盈利的 而且都是非常可观的 用我们国家的一句老话来说就是 兵欲善其事 必先利其器 '
                   '我觉得QQ对我这个新手来说十分友好 并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的',
 'review_datetime': '2025.06.06 20:33',
 'review_has_answer': False,
 'review_id': '38705502',
 'reviewer_id': 'noalc',
 'reviewer_name': 'Noalc',
 'reviewer_score': 22,
 'scraped_at': '2025-06-13T15:59:45.122344',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "Bonjour je viens d'acheter expert Quantum Queen MT5 c'est "
                   "un très bon expert il est très stable il n'y a des perte "
                   'que très minime et donne plus de bon profit je le trouve '
                   'excellent. Très bon service après vente pour '
                   "l'installation et les conseils de Bogdan Ion Puscasu il a "
                   'répondu à mes demandes et à mes messages avec une rapidité '
                   'exemplaire je suis très satisfait merci beaucoup.',
 'review_datetime': '2025.06.06 02:24',
 'review_has_answer': False,
 'review_id': '38679597',
 'reviewer_id': 'dnamron',
 'reviewer_name': 'Dnamron',
 'reviewer_score': 66,
 'scraped_at': '2025-06-13T15:59:45.125168',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '"This is an EA that allows you to trade with great peace '
                   'of mind." 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:43 # Thank you so much! That’s exactly what '
                   'I wanted to achieve with Quantum Queen — an EA that brings '
                   'not just results, but also peace of mind. Trading '
                   'shouldn’t be stressful, and I’m really glad you’re feeling '
                   'that sense of calm while using it. If you ever have '
                   'questions or need anything, feel free to reach out '
                   'anytime!',
 'review_datetime': '2025.06.04 08:33',
 'review_has_answer': False,
 'review_id': '38661377',
 'reviewer_id': 'jza80spl',
 'reviewer_name': 'jza80spl',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:59:45.128318',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '🌟 An Expert Advisor That Truly Delivers! 🌟 I’ve been using '
                   'Quantum Queen MT5 for a short time, but I’m already seeing '
                   'my account grow steadily. A colleague of mine has been '
                   'using it for over two months, and his results have been '
                   'exceptional and consistent, without hesitation. 🔹 It’s one '
                   'of the very few algobots on MetaTrader 5 that actually '
                   'works. 🔹 The developer is very professional and always '
                   'quick to respond to any questions. 🔹 It was recommended to '
                   'me by someone extremely meticulous… and it has exceeded '
                   'all expectations. 💰 It’s worth every cent of the price. '
                   'This is a professional tool, not just another empty '
                   'promise. 🔒 Reliable, profitable, and well-supported. '
                   'Highly recommended!',
 'review_datetime': '2025.06.06 23:02',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'saman83',
 'reviewer_name': 'Saman83',
 'reviewer_score': 79,
 'scraped_at': '2025-06-13T15:59:45.131153',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '53 SAMUEL DE SOUZA 2025.06.06 22:12 # Absolutely '
                   'Phenomenal – A True Queen in the Game! I’ve tested over 40 '
                   'different AEs in my trading journey, and I can confidently '
                   'say the Quantum Queen MT5 is in a league of its own. It’s '
                   'not just another gold (XAUUSD) AE—it’s a '
                   'precision-engineered powerhouse. From setup to execution, '
                   'everything runs smoothly and efficiently, adapting '
                   'perfectly to changing market conditions. Alongside The '
                   'Emperor, Quantum Queen is easily one of the two best AEs '
                   'I’ve ever come across. The consistency, reliability, and '
                   'intelligent trade management this bot brings are '
                   'unmatched. Whether you’re a seasoned trader or just '
                   'starting out, this is a must-have for your arsenal. Highly '
                   'recommend to anyone serious about mastering gold on MT5!',
 'review_datetime': '2025.06.06 22:12',
 'review_has_answer': False,
 'review_id': '38737428',
 'reviewer_id': 'swtsam2',
 'reviewer_name': 'SAMUEL DE SOUZA',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:59:45.133582',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '购买这个EA之后我基于最近半年短期做了很多次回测 根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 '
                   '每一次都是盈利的 而且都是非常可观的 用我们国家的一句老话来说就是 兵欲善其事 必先利其器 '
                   '我觉得QQ对我这个新手来说十分友好 并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的',
 'review_datetime': '2025.06.06 20:33',
 'review_has_answer': False,
 'review_id': '38705502',
 'reviewer_id': 'noalc',
 'reviewer_name': 'Noalc',
 'reviewer_score': 22,
 'scraped_at': '2025-06-13T15:59:45.135665',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "Bonjour je viens d'acheter expert Quantum Queen MT5 c'est "
                   "un très bon expert il est très stable il n'y a des perte "
                   'que très minime et donne plus de bon profit je le trouve '
                   'excellent. Très bon service après vente pour '
                   "l'installation et les conseils de Bogdan Ion Puscasu il a "
                   'répondu à mes demandes et à mes messages avec une rapidité '
                   'exemplaire je suis très satisfait merci beaucoup.',
 'review_datetime': '2025.06.06 02:24',
 'review_has_answer': False,
 'review_id': '38679597',
 'reviewer_id': 'dnamron',
 'reviewer_name': 'Dnamron',
 'reviewer_score': 66,
 'scraped_at': '2025-06-13T15:59:45.138181',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '🌟 An Expert Advisor That Truly Delivers! 🌟 I’ve been using '
                   'Quantum Queen MT5 for a short time, but I’m already seeing '
                   'my account grow steadily. A colleague of mine has been '
                   'using it for over two months, and his results have been '
                   'exceptional and consistent, without hesitation. 🔹 It’s one '
                   'of the very few algobots on MetaTrader 5 that actually '
                   'works. 🔹 The developer is very professional and always '
                   'quick to respond to any questions. 🔹 It was recommended to '
                   'me by someone extremely meticulous… and it has exceeded '
                   'all expectations. 💰 It’s worth every cent of the price. '
                   'This is a professional tool, not just another empty '
                   'promise. 🔒 Reliable, profitable, and well-supported. '
                   'Highly recommended!',
 'review_datetime': '2025.06.06 23:02',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'saman83',
 'reviewer_name': 'Saman83',
 'reviewer_score': 79,
 'scraped_at': '2025-06-13T15:59:45.142520',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '53 SAMUEL DE SOUZA 2025.06.06 22:12 # Absolutely '
                   'Phenomenal – A True Queen in the Game! I’ve tested over 40 '
                   'different AEs in my trading journey, and I can confidently '
                   'say the Quantum Queen MT5 is in a league of its own. It’s '
                   'not just another gold (XAUUSD) AE—it’s a '
                   'precision-engineered powerhouse. From setup to execution, '
                   'everything runs smoothly and efficiently, adapting '
                   'perfectly to changing market conditions. Alongside The '
                   'Emperor, Quantum Queen is easily one of the two best AEs '
                   'I’ve ever come across. The consistency, reliability, and '
                   'intelligent trade management this bot brings are '
                   'unmatched. Whether you’re a seasoned trader or just '
                   'starting out, this is a must-have for your arsenal. Highly '
                   'recommend to anyone serious about mastering gold on MT5!',
 'review_datetime': '2025.06.06 22:12',
 'review_has_answer': False,
 'review_id': '38737428',
 'reviewer_id': 'swtsam2',
 'reviewer_name': 'SAMUEL DE SOUZA',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:59:45.145017',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '购买这个EA之后我基于最近半年短期做了很多次回测 根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 '
                   '每一次都是盈利的 而且都是非常可观的 用我们国家的一句老话来说就是 兵欲善其事 必先利其器 '
                   '我觉得QQ对我这个新手来说十分友好 并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的',
 'review_datetime': '2025.06.06 20:33',
 'review_has_answer': False,
 'review_id': '38705502',
 'reviewer_id': 'noalc',
 'reviewer_name': 'Noalc',
 'reviewer_score': 22,
 'scraped_at': '2025-06-13T15:59:45.147101',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "Bonjour je viens d'acheter expert Quantum Queen MT5 c'est "
                   "un très bon expert il est très stable il n'y a des perte "
                   'que très minime et donne plus de bon profit je le trouve '
                   'excellent. Très bon service après vente pour '
                   "l'installation et les conseils de Bogdan Ion Puscasu il a "
                   'répondu à mes demandes et à mes messages avec une rapidité '
                   'exemplaire je suis très satisfait merci beaucoup.',
 'review_datetime': '2025.06.06 02:24',
 'review_has_answer': False,
 'review_id': '38679597',
 'reviewer_id': 'dnamron',
 'reviewer_name': 'Dnamron',
 'reviewer_score': 66,
 'scraped_at': '2025-06-13T15:59:45.149373',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '🌟 An Expert Advisor That Truly Delivers! 🌟 I’ve been using '
                   'Quantum Queen MT5 for a short time, but I’m already seeing '
                   'my account grow steadily. A colleague of mine has been '
                   'using it for over two months, and his results have been '
                   'exceptional and consistent, without hesitation. 🔹 It’s one '
                   'of the very few algobots on MetaTrader 5 that actually '
                   'works. 🔹 The developer is very professional and always '
                   'quick to respond to any questions. 🔹 It was recommended to '
                   'me by someone extremely meticulous… and it has exceeded '
                   'all expectations. 💰 It’s worth every cent of the price. '
                   'This is a professional tool, not just another empty '
                   'promise. 🔒 Reliable, profitable, and well-supported. '
                   'Highly recommended!',
 'review_datetime': '2025.06.06 23:02',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'saman83',
 'reviewer_name': 'Saman83',
 'reviewer_score': 79,
 'scraped_at': '2025-06-13T15:59:45.151692',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '53 SAMUEL DE SOUZA 2025.06.06 22:12 # Absolutely '
                   'Phenomenal – A True Queen in the Game! I’ve tested over 40 '
                   'different AEs in my trading journey, and I can confidently '
                   'say the Quantum Queen MT5 is in a league of its own. It’s '
                   'not just another gold (XAUUSD) AE—it’s a '
                   'precision-engineered powerhouse. From setup to execution, '
                   'everything runs smoothly and efficiently, adapting '
                   'perfectly to changing market conditions. Alongside The '
                   'Emperor, Quantum Queen is easily one of the two best AEs '
                   'I’ve ever come across. The consistency, reliability, and '
                   'intelligent trade management this bot brings are '
                   'unmatched. Whether you’re a seasoned trader or just '
                   'starting out, this is a must-have for your arsenal. Highly '
                   'recommend to anyone serious about mastering gold on MT5!',
 'review_datetime': '2025.06.06 22:12',
 'review_has_answer': False,
 'review_id': '38737428',
 'reviewer_id': 'swtsam2',
 'reviewer_name': 'SAMUEL DE SOUZA',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:59:45.153872',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '购买这个EA之后我基于最近半年短期做了很多次回测 根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 '
                   '每一次都是盈利的 而且都是非常可观的 用我们国家的一句老话来说就是 兵欲善其事 必先利其器 '
                   '我觉得QQ对我这个新手来说十分友好 并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的',
 'review_datetime': '2025.06.06 20:33',
 'review_has_answer': False,
 'review_id': '38705502',
 'reviewer_id': 'noalc',
 'reviewer_name': 'Noalc',
 'reviewer_score': 22,
 'scraped_at': '2025-06-13T15:59:45.156710',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "Bonjour je viens d'acheter expert Quantum Queen MT5 c'est "
                   "un très bon expert il est très stable il n'y a des perte "
                   'que très minime et donne plus de bon profit je le trouve '
                   'excellent. Très bon service après vente pour '
                   "l'installation et les conseils de Bogdan Ion Puscasu il a "
                   'répondu à mes demandes et à mes messages avec une rapidité '
                   'exemplaire je suis très satisfait merci beaucoup.',
 'review_datetime': '2025.06.06 02:24',
 'review_has_answer': False,
 'review_id': '38679597',
 'reviewer_id': 'dnamron',
 'reviewer_name': 'Dnamron',
 'reviewer_score': 66,
 'scraped_at': '2025-06-13T15:59:45.158703',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '"This is an EA that allows you to trade with great peace '
                   'of mind." 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:43 # Thank you so much! That’s exactly what '
                   'I wanted to achieve with Quantum Queen — an EA that brings '
                   'not just results, but also peace of mind. Trading '
                   'shouldn’t be stressful, and I’m really glad you’re feeling '
                   'that sense of calm while using it. If you ever have '
                   'questions or need anything, feel free to reach out '
                   'anytime!',
 'review_datetime': '2025.06.04 08:33',
 'review_has_answer': False,
 'review_id': '38661377',
 'reviewer_id': 'jza80spl',
 'reviewer_name': 'jza80spl',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:59:45.161755',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '48 goateeeth maker 2025.06.08 02:25 # ⭐⭐⭐⭐⭐ Excellent '
                   "EA—Profitable and Reliable! I've been using this EA on MT5 "
                   "for several weeks, and it's exceeded my expectations. The "
                   'performance has been consistent, generating steady profits '
                   'while effectively managing risks. Setup was '
                   'straightforward, and customer support has been responsive '
                   'whenever I had questions. Highly recommend this EA to '
                   'anyone looking for an automated, stress-free trading '
                   'solution!',
 'review_datetime': '2025.06.08 02:25',
 'review_has_answer': False,
 'review_id': '38333720',
 'reviewer_id': 'goateeeth',
 'reviewer_name': 'goateeeth maker',
 'reviewer_score': 48,
 'scraped_at': '2025-06-13T15:59:45.164071',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '🌟 An Expert Advisor That Truly Delivers! 🌟 I’ve been using '
                   'Quantum Queen MT5 for a short time, but I’m already seeing '
                   'my account grow steadily. A colleague of mine has been '
                   'using it for over two months, and his results have been '
                   'exceptional and consistent, without hesitation. 🔹 It’s one '
                   'of the very few algobots on MetaTrader 5 that actually '
                   'works. 🔹 The developer is very professional and always '
                   'quick to respond to any questions. 🔹 It was recommended to '
                   'me by someone extremely meticulous… and it has exceeded '
                   'all expectations. 💰 It’s worth every cent of the price. '
                   'This is a professional tool, not just another empty '
                   'promise. 🔒 Reliable, profitable, and well-supported. '
                   'Highly recommended!',
 'review_datetime': '2025.06.06 23:02',
 'review_has_answer': False,
 'review_id': '********',
 'reviewer_id': 'saman83',
 'reviewer_name': 'Saman83',
 'reviewer_score': 79,
 'scraped_at': '2025-06-13T15:59:45.166252',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '53 SAMUEL DE SOUZA 2025.06.06 22:12 # Absolutely '
                   'Phenomenal – A True Queen in the Game! I’ve tested over 40 '
                   'different AEs in my trading journey, and I can confidently '
                   'say the Quantum Queen MT5 is in a league of its own. It’s '
                   'not just another gold (XAUUSD) AE—it’s a '
                   'precision-engineered powerhouse. From setup to execution, '
                   'everything runs smoothly and efficiently, adapting '
                   'perfectly to changing market conditions. Alongside The '
                   'Emperor, Quantum Queen is easily one of the two best AEs '
                   'I’ve ever come across. The consistency, reliability, and '
                   'intelligent trade management this bot brings are '
                   'unmatched. Whether you’re a seasoned trader or just '
                   'starting out, this is a must-have for your arsenal. Highly '
                   'recommend to anyone serious about mastering gold on MT5!',
 'review_datetime': '2025.06.06 22:12',
 'review_has_answer': False,
 'review_id': '38737428',
 'reviewer_id': 'swtsam2',
 'reviewer_name': 'SAMUEL DE SOUZA',
 'reviewer_score': 53,
 'scraped_at': '2025-06-13T15:59:45.168408',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '购买这个EA之后我基于最近半年短期做了很多次回测 根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 '
                   '每一次都是盈利的 而且都是非常可观的 用我们国家的一句老话来说就是 兵欲善其事 必先利其器 '
                   '我觉得QQ对我这个新手来说十分友好 并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的',
 'review_datetime': '2025.06.06 20:33',
 'review_has_answer': False,
 'review_id': '38705502',
 'reviewer_id': 'noalc',
 'reviewer_name': 'Noalc',
 'reviewer_score': 22,
 'scraped_at': '2025-06-13T15:59:45.170505',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "Bonjour je viens d'acheter expert Quantum Queen MT5 c'est "
                   "un très bon expert il est très stable il n'y a des perte "
                   'que très minime et donne plus de bon profit je le trouve '
                   'excellent. Très bon service après vente pour '
                   "l'installation et les conseils de Bogdan Ion Puscasu il a "
                   'répondu à mes demandes et à mes messages avec une rapidité '
                   'exemplaire je suis très satisfait merci beaucoup.',
 'review_datetime': '2025.06.06 02:24',
 'review_has_answer': False,
 'review_id': '38679597',
 'reviewer_id': 'dnamron',
 'reviewer_name': 'Dnamron',
 'reviewer_score': 66,
 'scraped_at': '2025-06-13T15:59:45.172790',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '"This is an EA that allows you to trade with great peace '
                   'of mind." 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:43 # Thank you so much! That’s exactly what '
                   'I wanted to achieve with Quantum Queen — an EA that brings '
                   'not just results, but also peace of mind. Trading '
                   'shouldn’t be stressful, and I’m really glad you’re feeling '
                   'that sense of calm while using it. If you ever have '
                   'questions or need anything, feel free to reach out '
                   'anytime!',
 'review_datetime': '2025.06.04 08:33',
 'review_has_answer': False,
 'review_id': '38661377',
 'reviewer_id': 'jza80spl',
 'reviewer_name': 'jza80spl',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:59:45.195123',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '378 Guorong He 2025.06.03 16:06 # Very good grid system. '
                   'Thanks Bogdan！ 34750 Reply from developer Bogdan Ion '
                   'Puscasu 2025.06.05 14:42 # Thank you so much for your '
                   "review! I’m really glad to hear you're enjoying the grid "
                   'system. It’s designed to be both smart and steady, and I’m '
                   'happy it’s working well for you. Appreciate the kind words '
                   '– and if you ever need help or want to explore more '
                   'Quantum EAs, I’m just a message away!',
 'review_datetime': '2025.06.03 16:06',
 'review_has_answer': False,
 'review_id': '38691977',
 'reviewer_id': 'tokio147',
 'reviewer_name': 'Guorong He',
 'reviewer_score': 378,
 'scraped_at': '2025-06-13T15:59:45.198075',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.02 21:18',
 'review_has_answer': False,
 'review_id': '38596644',
 'reviewer_id': 'molobamllr',
 'reviewer_name': 'Lorenz Goeminne',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:59:45.201331',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bought the EA last week and already made about 4% in just '
                   'one week! Really happy with the performance so far. The '
                   'documentation provided is very detailed and informative — '
                   'it made setup and understanding the strategy very easy. '
                   'Looking forward to seeing more great results. 34750 Reply '
                   'from developer Bogdan Ion Puscasu 2025.06.05 14:37 # '
                   "That's fantastic to hear! I'm really glad the setup "
                   "process went smoothly and that you're already seeing solid "
                   'results in just one week — 4% is a great start! Thanks for '
                   'pointing out the documentation too, I put a lot of effort '
                   'into making it as clear and helpful as possible. Wishing '
                   'you continued success, and if you ever need anything, '
                   'don’t hesitate to reach out.',
 'review_datetime': '2025.06.02 13:36',
 'review_has_answer': False,
 'review_id': '38600643',
 'reviewer_id': 'nasoo',
 'reviewer_name': 'Nasoo',
 'reviewer_score': 785,
 'scraped_at': '2025-06-13T15:59:45.205251',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "Bonjour je viens d'acheter expert Quantum Queen MT5 c'est "
                   "un très bon expert il est très stable il n'y a des perte "
                   'que très minime et donne plus de bon profit je le trouve '
                   'excellent. Très bon service après vente pour '
                   "l'installation et les conseils de Bogdan Ion Puscasu il a "
                   'répondu à mes demandes et à mes messages avec une rapidité '
                   'exemplaire je suis très satisfait merci beaucoup.',
 'review_datetime': '2025.06.06 02:24',
 'review_has_answer': False,
 'review_id': '38679597',
 'reviewer_id': 'dnamron',
 'reviewer_name': 'Dnamron',
 'reviewer_score': 66,
 'scraped_at': '2025-06-13T15:59:45.208550',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '"This is an EA that allows you to trade with great peace '
                   'of mind." 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:43 # Thank you so much! That’s exactly what '
                   'I wanted to achieve with Quantum Queen — an EA that brings '
                   'not just results, but also peace of mind. Trading '
                   'shouldn’t be stressful, and I’m really glad you’re feeling '
                   'that sense of calm while using it. If you ever have '
                   'questions or need anything, feel free to reach out '
                   'anytime!',
 'review_datetime': '2025.06.04 08:33',
 'review_has_answer': False,
 'review_id': '38661377',
 'reviewer_id': 'jza80spl',
 'reviewer_name': 'jza80spl',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:59:45.212798',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '378 Guorong He 2025.06.03 16:06 # Very good grid system. '
                   'Thanks Bogdan！ 34750 Reply from developer Bogdan Ion '
                   'Puscasu 2025.06.05 14:42 # Thank you so much for your '
                   "review! I’m really glad to hear you're enjoying the grid "
                   'system. It’s designed to be both smart and steady, and I’m '
                   'happy it’s working well for you. Appreciate the kind words '
                   '– and if you ever need help or want to explore more '
                   'Quantum EAs, I’m just a message away!',
 'review_datetime': '2025.06.03 16:06',
 'review_has_answer': False,
 'review_id': '38691977',
 'reviewer_id': 'tokio147',
 'reviewer_name': 'Guorong He',
 'reviewer_score': 378,
 'scraped_at': '2025-06-13T15:59:45.216793',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "Bonjour je viens d'acheter expert Quantum Queen MT5 c'est "
                   "un très bon expert il est très stable il n'y a des perte "
                   'que très minime et donne plus de bon profit je le trouve '
                   'excellent. Très bon service après vente pour '
                   "l'installation et les conseils de Bogdan Ion Puscasu il a "
                   'répondu à mes demandes et à mes messages avec une rapidité '
                   'exemplaire je suis très satisfait merci beaucoup.',
 'review_datetime': '2025.06.06 02:24',
 'review_has_answer': False,
 'review_id': '38679597',
 'reviewer_id': 'dnamron',
 'reviewer_name': 'Dnamron',
 'reviewer_score': 66,
 'scraped_at': '2025-06-13T15:59:45.219085',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '"This is an EA that allows you to trade with great peace '
                   'of mind." 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:43 # Thank you so much! That’s exactly what '
                   'I wanted to achieve with Quantum Queen — an EA that brings '
                   'not just results, but also peace of mind. Trading '
                   'shouldn’t be stressful, and I’m really glad you’re feeling '
                   'that sense of calm while using it. If you ever have '
                   'questions or need anything, feel free to reach out '
                   'anytime!',
 'review_datetime': '2025.06.04 08:33',
 'review_has_answer': False,
 'review_id': '38661377',
 'reviewer_id': 'jza80spl',
 'reviewer_name': 'jza80spl',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:59:45.221990',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '378 Guorong He 2025.06.03 16:06 # Very good grid system. '
                   'Thanks Bogdan！ 34750 Reply from developer Bogdan Ion '
                   'Puscasu 2025.06.05 14:42 # Thank you so much for your '
                   "review! I’m really glad to hear you're enjoying the grid "
                   'system. It’s designed to be both smart and steady, and I’m '
                   'happy it’s working well for you. Appreciate the kind words '
                   '– and if you ever need help or want to explore more '
                   'Quantum EAs, I’m just a message away!',
 'review_datetime': '2025.06.03 16:06',
 'review_has_answer': False,
 'review_id': '38691977',
 'reviewer_id': 'tokio147',
 'reviewer_name': 'Guorong He',
 'reviewer_score': 378,
 'scraped_at': '2025-06-13T15:59:45.224775',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.02 21:18',
 'review_has_answer': False,
 'review_id': '38596644',
 'reviewer_id': 'molobamllr',
 'reviewer_name': 'Lorenz Goeminne',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:59:45.228064',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '378 Guorong He 2025.06.03 16:06 # Very good grid system. '
                   'Thanks Bogdan！ 34750 Reply from developer Bogdan Ion '
                   'Puscasu 2025.06.05 14:42 # Thank you so much for your '
                   "review! I’m really glad to hear you're enjoying the grid "
                   'system. It’s designed to be both smart and steady, and I’m '
                   'happy it’s working well for you. Appreciate the kind words '
                   '– and if you ever need help or want to explore more '
                   'Quantum EAs, I’m just a message away!',
 'review_datetime': '2025.06.03 16:06',
 'review_has_answer': False,
 'review_id': '38691977',
 'reviewer_id': 'tokio147',
 'reviewer_name': 'Guorong He',
 'reviewer_score': 378,
 'scraped_at': '2025-06-13T15:59:45.230983',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.02 21:18',
 'review_has_answer': False,
 'review_id': '38596644',
 'reviewer_id': 'molobamllr',
 'reviewer_name': 'Lorenz Goeminne',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:59:45.234406',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bought the EA last week and already made about 4% in just '
                   'one week! Really happy with the performance so far. The '
                   'documentation provided is very detailed and informative — '
                   'it made setup and understanding the strategy very easy. '
                   'Looking forward to seeing more great results. 34750 Reply '
                   'from developer Bogdan Ion Puscasu 2025.06.05 14:37 # '
                   "That's fantastic to hear! I'm really glad the setup "
                   "process went smoothly and that you're already seeing solid "
                   'results in just one week — 4% is a great start! Thanks for '
                   'pointing out the documentation too, I put a lot of effort '
                   'into making it as clear and helpful as possible. Wishing '
                   'you continued success, and if you ever need anything, '
                   'don’t hesitate to reach out.',
 'review_datetime': '2025.06.02 13:36',
 'review_has_answer': False,
 'review_id': '38600643',
 'reviewer_id': 'nasoo',
 'reviewer_name': 'Nasoo',
 'reviewer_score': 785,
 'scraped_at': '2025-06-13T15:59:45.237207',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I am very satisfied with the results from the 1st week, '
                   'its the same as in the backtests. I fully recommend it. '
                   '34750 Reply from developer Bogdan Ion Puscasu 2025.06.05 '
                   "14:35 # Thank you so much for the great feedback! I'm "
                   'really glad to hear that your first week with Quantum '
                   "Queen matches the backtests — that's always a great sign. "
                   "It means a lot to know you're satisfied and confident in "
                   'the EA. Wishing you continued success and steady profits '
                   "ahead! If you ever need help or have questions, I'm here.",
 'review_datetime': '2025.06.01 15:52',
 'review_has_answer': False,
 'review_id': '38580848',
 'reviewer_id': 'francesc00',
 'reviewer_name': 'Francesc00',
 'reviewer_score': 34,
 'scraped_at': '2025-06-13T15:59:45.240318',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '"This is an EA that allows you to trade with great peace '
                   'of mind." 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:43 # Thank you so much! That’s exactly what '
                   'I wanted to achieve with Quantum Queen — an EA that brings '
                   'not just results, but also peace of mind. Trading '
                   'shouldn’t be stressful, and I’m really glad you’re feeling '
                   'that sense of calm while using it. If you ever have '
                   'questions or need anything, feel free to reach out '
                   'anytime!',
 'review_datetime': '2025.06.04 08:33',
 'review_has_answer': False,
 'review_id': '38661377',
 'reviewer_id': 'jza80spl',
 'reviewer_name': 'jza80spl',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:59:45.243136',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '378 Guorong He 2025.06.03 16:06 # Very good grid system. '
                   'Thanks Bogdan！ 34750 Reply from developer Bogdan Ion '
                   'Puscasu 2025.06.05 14:42 # Thank you so much for your '
                   "review! I’m really glad to hear you're enjoying the grid "
                   'system. It’s designed to be both smart and steady, and I’m '
                   'happy it’s working well for you. Appreciate the kind words '
                   '– and if you ever need help or want to explore more '
                   'Quantum EAs, I’m just a message away!',
 'review_datetime': '2025.06.03 16:06',
 'review_has_answer': False,
 'review_id': '38691977',
 'reviewer_id': 'tokio147',
 'reviewer_name': 'Guorong He',
 'reviewer_score': 378,
 'scraped_at': '2025-06-13T15:59:45.246424',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.02 21:18',
 'review_has_answer': False,
 'review_id': '38596644',
 'reviewer_id': 'molobamllr',
 'reviewer_name': 'Lorenz Goeminne',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:59:45.249844',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bought the EA last week and already made about 4% in just '
                   'one week! Really happy with the performance so far. The '
                   'documentation provided is very detailed and informative — '
                   'it made setup and understanding the strategy very easy. '
                   'Looking forward to seeing more great results. 34750 Reply '
                   'from developer Bogdan Ion Puscasu 2025.06.05 14:37 # '
                   "That's fantastic to hear! I'm really glad the setup "
                   "process went smoothly and that you're already seeing solid "
                   'results in just one week — 4% is a great start! Thanks for '
                   'pointing out the documentation too, I put a lot of effort '
                   'into making it as clear and helpful as possible. Wishing '
                   'you continued success, and if you ever need anything, '
                   'don’t hesitate to reach out.',
 'review_datetime': '2025.06.02 13:36',
 'review_has_answer': False,
 'review_id': '38600643',
 'reviewer_id': 'nasoo',
 'reviewer_name': 'Nasoo',
 'reviewer_score': 785,
 'scraped_at': '2025-06-13T15:59:45.253060',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '"This is an EA that allows you to trade with great peace '
                   'of mind." 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:43 # Thank you so much! That’s exactly what '
                   'I wanted to achieve with Quantum Queen — an EA that brings '
                   'not just results, but also peace of mind. Trading '
                   'shouldn’t be stressful, and I’m really glad you’re feeling '
                   'that sense of calm while using it. If you ever have '
                   'questions or need anything, feel free to reach out '
                   'anytime!',
 'review_datetime': '2025.06.04 08:33',
 'review_has_answer': False,
 'review_id': '38661377',
 'reviewer_id': 'jza80spl',
 'reviewer_name': 'jza80spl',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:59:45.256080',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '378 Guorong He 2025.06.03 16:06 # Very good grid system. '
                   'Thanks Bogdan！ 34750 Reply from developer Bogdan Ion '
                   'Puscasu 2025.06.05 14:42 # Thank you so much for your '
                   "review! I’m really glad to hear you're enjoying the grid "
                   'system. It’s designed to be both smart and steady, and I’m '
                   'happy it’s working well for you. Appreciate the kind words '
                   '– and if you ever need help or want to explore more '
                   'Quantum EAs, I’m just a message away!',
 'review_datetime': '2025.06.03 16:06',
 'review_has_answer': False,
 'review_id': '38691977',
 'reviewer_id': 'tokio147',
 'reviewer_name': 'Guorong He',
 'reviewer_score': 378,
 'scraped_at': '2025-06-13T15:59:45.259107',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.02 21:18',
 'review_has_answer': False,
 'review_id': '38596644',
 'reviewer_id': 'molobamllr',
 'reviewer_name': 'Lorenz Goeminne',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:59:45.262164',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bought the EA last week and already made about 4% in just '
                   'one week! Really happy with the performance so far. The '
                   'documentation provided is very detailed and informative — '
                   'it made setup and understanding the strategy very easy. '
                   'Looking forward to seeing more great results. 34750 Reply '
                   'from developer Bogdan Ion Puscasu 2025.06.05 14:37 # '
                   "That's fantastic to hear! I'm really glad the setup "
                   "process went smoothly and that you're already seeing solid "
                   'results in just one week — 4% is a great start! Thanks for '
                   'pointing out the documentation too, I put a lot of effort '
                   'into making it as clear and helpful as possible. Wishing '
                   'you continued success, and if you ever need anything, '
                   'don’t hesitate to reach out.',
 'review_datetime': '2025.06.02 13:36',
 'review_has_answer': False,
 'review_id': '38600643',
 'reviewer_id': 'nasoo',
 'reviewer_name': 'Nasoo',
 'reviewer_score': 785,
 'scraped_at': '2025-06-13T15:59:45.265242',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '378 Guorong He 2025.06.03 16:06 # Very good grid system. '
                   'Thanks Bogdan！ 34750 Reply from developer Bogdan Ion '
                   'Puscasu 2025.06.05 14:42 # Thank you so much for your '
                   "review! I’m really glad to hear you're enjoying the grid "
                   'system. It’s designed to be both smart and steady, and I’m '
                   'happy it’s working well for you. Appreciate the kind words '
                   '– and if you ever need help or want to explore more '
                   'Quantum EAs, I’m just a message away!',
 'review_datetime': '2025.06.03 16:06',
 'review_has_answer': False,
 'review_id': '38691977',
 'reviewer_id': 'tokio147',
 'reviewer_name': 'Guorong He',
 'reviewer_score': 378,
 'scraped_at': '2025-06-13T15:59:45.268242',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.02 21:18',
 'review_has_answer': False,
 'review_id': '38596644',
 'reviewer_id': 'molobamllr',
 'reviewer_name': 'Lorenz Goeminne',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:59:45.271851',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bought the EA last week and already made about 4% in just '
                   'one week! Really happy with the performance so far. The '
                   'documentation provided is very detailed and informative — '
                   'it made setup and understanding the strategy very easy. '
                   'Looking forward to seeing more great results. 34750 Reply '
                   'from developer Bogdan Ion Puscasu 2025.06.05 14:37 # '
                   "That's fantastic to hear! I'm really glad the setup "
                   "process went smoothly and that you're already seeing solid "
                   'results in just one week — 4% is a great start! Thanks for '
                   'pointing out the documentation too, I put a lot of effort '
                   'into making it as clear and helpful as possible. Wishing '
                   'you continued success, and if you ever need anything, '
                   'don’t hesitate to reach out.',
 'review_datetime': '2025.06.02 13:36',
 'review_has_answer': False,
 'review_id': '38600643',
 'reviewer_id': 'nasoo',
 'reviewer_name': 'Nasoo',
 'reviewer_score': 785,
 'scraped_at': '2025-06-13T15:59:45.274415',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I am very satisfied with the results from the 1st week, '
                   'its the same as in the backtests. I fully recommend it. '
                   '34750 Reply from developer Bogdan Ion Puscasu 2025.06.05 '
                   "14:35 # Thank you so much for the great feedback! I'm "
                   'really glad to hear that your first week with Quantum '
                   "Queen matches the backtests — that's always a great sign. "
                   "It means a lot to know you're satisfied and confident in "
                   'the EA. Wishing you continued success and steady profits '
                   "ahead! If you ever need help or have questions, I'm here.",
 'review_datetime': '2025.06.01 15:52',
 'review_has_answer': False,
 'review_id': '38580848',
 'reviewer_id': 'francesc00',
 'reviewer_name': 'Francesc00',
 'reviewer_score': 34,
 'scraped_at': '2025-06-13T15:59:45.279432',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '"This is an EA that allows you to trade with great peace '
                   'of mind." 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:43 # Thank you so much! That’s exactly what '
                   'I wanted to achieve with Quantum Queen — an EA that brings '
                   'not just results, but also peace of mind. Trading '
                   'shouldn’t be stressful, and I’m really glad you’re feeling '
                   'that sense of calm while using it. If you ever have '
                   'questions or need anything, feel free to reach out '
                   'anytime!',
 'review_datetime': '2025.06.04 08:33',
 'review_has_answer': False,
 'review_id': '38661377',
 'reviewer_id': 'jza80spl',
 'reviewer_name': 'jza80spl',
 'reviewer_score': 19,
 'scraped_at': '2025-06-13T15:59:45.284526',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '378 Guorong He 2025.06.03 16:06 # Very good grid system. '
                   'Thanks Bogdan！ 34750 Reply from developer Bogdan Ion '
                   'Puscasu 2025.06.05 14:42 # Thank you so much for your '
                   "review! I’m really glad to hear you're enjoying the grid "
                   'system. It’s designed to be both smart and steady, and I’m '
                   'happy it’s working well for you. Appreciate the kind words '
                   '– and if you ever need help or want to explore more '
                   'Quantum EAs, I’m just a message away!',
 'review_datetime': '2025.06.03 16:06',
 'review_has_answer': False,
 'review_id': '38691977',
 'reviewer_id': 'tokio147',
 'reviewer_name': 'Guorong He',
 'reviewer_score': 378,
 'scraped_at': '2025-06-13T15:59:45.287664',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.02 21:18',
 'review_has_answer': False,
 'review_id': '38596644',
 'reviewer_id': 'molobamllr',
 'reviewer_name': 'Lorenz Goeminne',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:59:45.291471',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I am very satisfied with the results from the 1st week, '
                   'its the same as in the backtests. I fully recommend it. '
                   '34750 Reply from developer Bogdan Ion Puscasu 2025.06.05 '
                   "14:35 # Thank you so much for the great feedback! I'm "
                   'really glad to hear that your first week with Quantum '
                   "Queen matches the backtests — that's always a great sign. "
                   "It means a lot to know you're satisfied and confident in "
                   'the EA. Wishing you continued success and steady profits '
                   "ahead! If you ever need help or have questions, I'm here.",
 'review_datetime': '2025.06.01 15:52',
 'review_has_answer': False,
 'review_id': '38580848',
 'reviewer_id': 'francesc00',
 'reviewer_name': 'Francesc00',
 'reviewer_score': 34,
 'scraped_at': '2025-06-13T15:59:45.316580',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.05.31 06:14',
 'review_has_answer': False,
 'review_id': '38668358',
 'reviewer_id': 'schnitzler88',
 'reviewer_name': 'Extoflex',
 'reviewer_score': 328,
 'scraped_at': '2025-06-13T15:59:45.320545',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '40 Lucas Ramos 2025.05.30 09:43 # O Bogdan é um Trader '
                   'extremamente responsável e simpático e oferece suporte '
                   'rápido, a Quantum Queen é uma ótima EA que já me rendeu '
                   'bons lucros com baixos riscos, recomendo fortemente o uso '
                   'dessa EA para Traders responsáveis e que queiram fazer seu '
                   'dinheiro render sem muito esforço 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:33 # Thank you '
                   "so much for your kind words and trust, Lucas! 🙏 I'm really "
                   'happy to hear that Quantum Queen is delivering good '
                   'profits with low risk for you — exactly what I designed it '
                   'for. I truly appreciate your support and recommendation, '
                   "and I'm always here if you need help or have any "
                   'questions. Wishing you continued success and steady growth '
                   "— let's keep making the most out of every trade!",
 'review_datetime': '2025.05.30 09:43',
 'review_has_answer': False,
 'review_id': '38537336',
 'reviewer_id': 'lucasramoss',
 'reviewer_name': 'Lucas Ramos',
 'reviewer_score': 40,
 'scraped_at': '2025-06-13T15:59:45.323869',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.06.02 21:18',
 'review_has_answer': False,
 'review_id': '38596644',
 'reviewer_id': 'molobamllr',
 'reviewer_name': 'Lorenz Goeminne',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:59:45.327329',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bought the EA last week and already made about 4% in just '
                   'one week! Really happy with the performance so far. The '
                   'documentation provided is very detailed and informative — '
                   'it made setup and understanding the strategy very easy. '
                   'Looking forward to seeing more great results. 34750 Reply '
                   'from developer Bogdan Ion Puscasu 2025.06.05 14:37 # '
                   "That's fantastic to hear! I'm really glad the setup "
                   "process went smoothly and that you're already seeing solid "
                   'results in just one week — 4% is a great start! Thanks for '
                   'pointing out the documentation too, I put a lot of effort '
                   'into making it as clear and helpful as possible. Wishing '
                   'you continued success, and if you ever need anything, '
                   'don’t hesitate to reach out.',
 'review_datetime': '2025.06.02 13:36',
 'review_has_answer': False,
 'review_id': '38600643',
 'reviewer_id': 'nasoo',
 'reviewer_name': 'Nasoo',
 'reviewer_score': 785,
 'scraped_at': '2025-06-13T15:59:45.330412',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I am very satisfied with the results from the 1st week, '
                   'its the same as in the backtests. I fully recommend it. '
                   '34750 Reply from developer Bogdan Ion Puscasu 2025.06.05 '
                   "14:35 # Thank you so much for the great feedback! I'm "
                   'really glad to hear that your first week with Quantum '
                   "Queen matches the backtests — that's always a great sign. "
                   "It means a lot to know you're satisfied and confident in "
                   'the EA. Wishing you continued success and steady profits '
                   "ahead! If you ever need help or have questions, I'm here.",
 'review_datetime': '2025.06.01 15:52',
 'review_has_answer': False,
 'review_id': '38580848',
 'reviewer_id': 'francesc00',
 'reviewer_name': 'Francesc00',
 'reviewer_score': 34,
 'scraped_at': '2025-06-13T15:59:45.333413',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.05.31 06:14',
 'review_has_answer': False,
 'review_id': '38668358',
 'reviewer_id': 'schnitzler88',
 'reviewer_name': 'Extoflex',
 'reviewer_score': 328,
 'scraped_at': '2025-06-13T15:59:45.337194',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bought the EA last week and already made about 4% in just '
                   'one week! Really happy with the performance so far. The '
                   'documentation provided is very detailed and informative — '
                   'it made setup and understanding the strategy very easy. '
                   'Looking forward to seeing more great results. 34750 Reply '
                   'from developer Bogdan Ion Puscasu 2025.06.05 14:37 # '
                   "That's fantastic to hear! I'm really glad the setup "
                   "process went smoothly and that you're already seeing solid "
                   'results in just one week — 4% is a great start! Thanks for '
                   'pointing out the documentation too, I put a lot of effort '
                   'into making it as clear and helpful as possible. Wishing '
                   'you continued success, and if you ever need anything, '
                   'don’t hesitate to reach out.',
 'review_datetime': '2025.06.02 13:36',
 'review_has_answer': False,
 'review_id': '38600643',
 'reviewer_id': 'nasoo',
 'reviewer_name': 'Nasoo',
 'reviewer_score': 785,
 'scraped_at': '2025-06-13T15:59:45.340102',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I am very satisfied with the results from the 1st week, '
                   'its the same as in the backtests. I fully recommend it. '
                   '34750 Reply from developer Bogdan Ion Puscasu 2025.06.05 '
                   "14:35 # Thank you so much for the great feedback! I'm "
                   'really glad to hear that your first week with Quantum '
                   "Queen matches the backtests — that's always a great sign. "
                   "It means a lot to know you're satisfied and confident in "
                   'the EA. Wishing you continued success and steady profits '
                   "ahead! If you ever need help or have questions, I'm here.",
 'review_datetime': '2025.06.01 15:52',
 'review_has_answer': False,
 'review_id': '38580848',
 'reviewer_id': 'francesc00',
 'reviewer_name': 'Francesc00',
 'reviewer_score': 34,
 'scraped_at': '2025-06-13T15:59:45.343129',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.05.31 06:14',
 'review_has_answer': False,
 'review_id': '38668358',
 'reviewer_id': 'schnitzler88',
 'reviewer_name': 'Extoflex',
 'reviewer_score': 328,
 'scraped_at': '2025-06-13T15:59:45.346766',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '40 Lucas Ramos 2025.05.30 09:43 # O Bogdan é um Trader '
                   'extremamente responsável e simpático e oferece suporte '
                   'rápido, a Quantum Queen é uma ótima EA que já me rendeu '
                   'bons lucros com baixos riscos, recomendo fortemente o uso '
                   'dessa EA para Traders responsáveis e que queiram fazer seu '
                   'dinheiro render sem muito esforço 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:33 # Thank you '
                   "so much for your kind words and trust, Lucas! 🙏 I'm really "
                   'happy to hear that Quantum Queen is delivering good '
                   'profits with low risk for you — exactly what I designed it '
                   'for. I truly appreciate your support and recommendation, '
                   "and I'm always here if you need help or have any "
                   'questions. Wishing you continued success and steady growth '
                   "— let's keep making the most out of every trade!",
 'review_datetime': '2025.05.30 09:43',
 'review_has_answer': False,
 'review_id': '38537336',
 'reviewer_id': 'lucasramoss',
 'reviewer_name': 'Lucas Ramos',
 'reviewer_score': 40,
 'scraped_at': '2025-06-13T15:59:45.350357',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.05.31 06:14',
 'review_has_answer': False,
 'review_id': '38668358',
 'reviewer_id': 'schnitzler88',
 'reviewer_name': 'Extoflex',
 'reviewer_score': 328,
 'scraped_at': '2025-06-13T15:59:45.354447',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '40 Lucas Ramos 2025.05.30 09:43 # O Bogdan é um Trader '
                   'extremamente responsável e simpático e oferece suporte '
                   'rápido, a Quantum Queen é uma ótima EA que já me rendeu '
                   'bons lucros com baixos riscos, recomendo fortemente o uso '
                   'dessa EA para Traders responsáveis e que queiram fazer seu '
                   'dinheiro render sem muito esforço 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:33 # Thank you '
                   "so much for your kind words and trust, Lucas! 🙏 I'm really "
                   'happy to hear that Quantum Queen is delivering good '
                   'profits with low risk for you — exactly what I designed it '
                   'for. I truly appreciate your support and recommendation, '
                   "and I'm always here if you need help or have any "
                   'questions. Wishing you continued success and steady growth '
                   "— let's keep making the most out of every trade!",
 'review_datetime': '2025.05.30 09:43',
 'review_has_answer': False,
 'review_id': '38537336',
 'reviewer_id': 'lucasramoss',
 'reviewer_name': 'Lucas Ramos',
 'reviewer_score': 40,
 'scraped_at': '2025-06-13T15:59:45.357640',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bogdan has been incredibly responsive, often replying '
                   'within minutes, which makes it feel like I have a tech '
                   'team right beside me — something I really appreciate. I’ve '
                   'been running the EA from May 26th to May 29th, and it '
                   'delivered a 2% profit within just four days using the '
                   'default risk settings. It doesn’t use a martingale '
                   'strategy, but it does attempt to average price levels in a '
                   'controlled manner. I especially like that it only executes '
                   'trades when specific market conditions are met, aiming for '
                   'higher probability setups. So far, the performance has '
                   'been solid and feels safe. Highly recommended, and based '
                   'on these results, I plan to explore more of their '
                   'products. 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:31 # Thank you so much for the thoughtful '
                   'and detailed feedback, Leo! 🙏 I’m really glad to hear that '
                   'you appreciate the way Quantum Queen handles trades — '
                   'focused, careful, and without martingale risk. I’ve spent '
                   'a lot of time fine-tuning the strategy to be both smart '
                   'and safe, and it’s great to know it’s performing well for '
                   'you. I’m also always happy to help, so don’t hesitate to '
                   'reach out anytime. Looking forward to having you explore '
                   'the rest of the Quantum family — there’s more good stuff '
                   'waiting!',
 'review_datetime': '2025.05.30 08:56',
 'review_has_answer': False,
 'review_id': '38237707',
 'reviewer_id': 'chasuhun23',
 'reviewer_name': 'Leo',
 'reviewer_score': 56,
 'scraped_at': '2025-06-13T15:59:45.360924',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I am very satisfied with the results from the 1st week, '
                   'its the same as in the backtests. I fully recommend it. '
                   '34750 Reply from developer Bogdan Ion Puscasu 2025.06.05 '
                   "14:35 # Thank you so much for the great feedback! I'm "
                   'really glad to hear that your first week with Quantum '
                   "Queen matches the backtests — that's always a great sign. "
                   "It means a lot to know you're satisfied and confident in "
                   'the EA. Wishing you continued success and steady profits '
                   "ahead! If you ever need help or have questions, I'm here.",
 'review_datetime': '2025.06.01 15:52',
 'review_has_answer': False,
 'review_id': '38580848',
 'reviewer_id': 'francesc00',
 'reviewer_name': 'Francesc00',
 'reviewer_score': 34,
 'scraped_at': '2025-06-13T15:59:45.364336',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.05.31 06:14',
 'review_has_answer': False,
 'review_id': '38668358',
 'reviewer_id': 'schnitzler88',
 'reviewer_name': 'Extoflex',
 'reviewer_score': 328,
 'scraped_at': '2025-06-13T15:59:45.369257',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '40 Lucas Ramos 2025.05.30 09:43 # O Bogdan é um Trader '
                   'extremamente responsável e simpático e oferece suporte '
                   'rápido, a Quantum Queen é uma ótima EA que já me rendeu '
                   'bons lucros com baixos riscos, recomendo fortemente o uso '
                   'dessa EA para Traders responsáveis e que queiram fazer seu '
                   'dinheiro render sem muito esforço 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:33 # Thank you '
                   "so much for your kind words and trust, Lucas! 🙏 I'm really "
                   'happy to hear that Quantum Queen is delivering good '
                   'profits with low risk for you — exactly what I designed it '
                   'for. I truly appreciate your support and recommendation, '
                   "and I'm always here if you need help or have any "
                   'questions. Wishing you continued success and steady growth '
                   "— let's keep making the most out of every trade!",
 'review_datetime': '2025.05.30 09:43',
 'review_has_answer': False,
 'review_id': '38537336',
 'reviewer_id': 'lucasramoss',
 'reviewer_name': 'Lucas Ramos',
 'reviewer_score': 40,
 'scraped_at': '2025-06-13T15:59:45.372317',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I am very satisfied with the results from the 1st week, '
                   'its the same as in the backtests. I fully recommend it. '
                   '34750 Reply from developer Bogdan Ion Puscasu 2025.06.05 '
                   "14:35 # Thank you so much for the great feedback! I'm "
                   'really glad to hear that your first week with Quantum '
                   "Queen matches the backtests — that's always a great sign. "
                   "It means a lot to know you're satisfied and confident in "
                   'the EA. Wishing you continued success and steady profits '
                   "ahead! If you ever need help or have questions, I'm here.",
 'review_datetime': '2025.06.01 15:52',
 'review_has_answer': False,
 'review_id': '38580848',
 'reviewer_id': 'francesc00',
 'reviewer_name': 'Francesc00',
 'reviewer_score': 34,
 'scraped_at': '2025-06-13T15:59:45.375221',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.05.31 06:14',
 'review_has_answer': False,
 'review_id': '38668358',
 'reviewer_id': 'schnitzler88',
 'reviewer_name': 'Extoflex',
 'reviewer_score': 328,
 'scraped_at': '2025-06-13T15:59:45.378486',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '40 Lucas Ramos 2025.05.30 09:43 # O Bogdan é um Trader '
                   'extremamente responsável e simpático e oferece suporte '
                   'rápido, a Quantum Queen é uma ótima EA que já me rendeu '
                   'bons lucros com baixos riscos, recomendo fortemente o uso '
                   'dessa EA para Traders responsáveis e que queiram fazer seu '
                   'dinheiro render sem muito esforço 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:33 # Thank you '
                   "so much for your kind words and trust, Lucas! 🙏 I'm really "
                   'happy to hear that Quantum Queen is delivering good '
                   'profits with low risk for you — exactly what I designed it '
                   'for. I truly appreciate your support and recommendation, '
                   "and I'm always here if you need help or have any "
                   'questions. Wishing you continued success and steady growth '
                   "— let's keep making the most out of every trade!",
 'review_datetime': '2025.05.30 09:43',
 'review_has_answer': False,
 'review_id': '38537336',
 'reviewer_id': 'lucasramoss',
 'reviewer_name': 'Lucas Ramos',
 'reviewer_score': 40,
 'scraped_at': '2025-06-13T15:59:45.381600',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bogdan has been incredibly responsive, often replying '
                   'within minutes, which makes it feel like I have a tech '
                   'team right beside me — something I really appreciate. I’ve '
                   'been running the EA from May 26th to May 29th, and it '
                   'delivered a 2% profit within just four days using the '
                   'default risk settings. It doesn’t use a martingale '
                   'strategy, but it does attempt to average price levels in a '
                   'controlled manner. I especially like that it only executes '
                   'trades when specific market conditions are met, aiming for '
                   'higher probability setups. So far, the performance has '
                   'been solid and feels safe. Highly recommended, and based '
                   'on these results, I plan to explore more of their '
                   'products. 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:31 # Thank you so much for the thoughtful '
                   'and detailed feedback, Leo! 🙏 I’m really glad to hear that '
                   'you appreciate the way Quantum Queen handles trades — '
                   'focused, careful, and without martingale risk. I’ve spent '
                   'a lot of time fine-tuning the strategy to be both smart '
                   'and safe, and it’s great to know it’s performing well for '
                   'you. I’m also always happy to help, so don’t hesitate to '
                   'reach out anytime. Looking forward to having you explore '
                   'the rest of the Quantum family — there’s more good stuff '
                   'waiting!',
 'review_datetime': '2025.05.30 08:56',
 'review_has_answer': False,
 'review_id': '38237707',
 'reviewer_id': 'chasuhun23',
 'reviewer_name': 'Leo',
 'reviewer_score': 56,
 'scraped_at': '2025-06-13T15:59:45.384660',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.05.31 06:14',
 'review_has_answer': False,
 'review_id': '38668358',
 'reviewer_id': 'schnitzler88',
 'reviewer_name': 'Extoflex',
 'reviewer_score': 328,
 'scraped_at': '2025-06-13T15:59:45.388297',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '40 Lucas Ramos 2025.05.30 09:43 # O Bogdan é um Trader '
                   'extremamente responsável e simpático e oferece suporte '
                   'rápido, a Quantum Queen é uma ótima EA que já me rendeu '
                   'bons lucros com baixos riscos, recomendo fortemente o uso '
                   'dessa EA para Traders responsáveis e que queiram fazer seu '
                   'dinheiro render sem muito esforço 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:33 # Thank you '
                   "so much for your kind words and trust, Lucas! 🙏 I'm really "
                   'happy to hear that Quantum Queen is delivering good '
                   'profits with low risk for you — exactly what I designed it '
                   'for. I truly appreciate your support and recommendation, '
                   "and I'm always here if you need help or have any "
                   'questions. Wishing you continued success and steady growth '
                   "— let's keep making the most out of every trade!",
 'review_datetime': '2025.05.30 09:43',
 'review_has_answer': False,
 'review_id': '38537336',
 'reviewer_id': 'lucasramoss',
 'reviewer_name': 'Lucas Ramos',
 'reviewer_score': 40,
 'scraped_at': '2025-06-13T15:59:45.391174',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bogdan has been incredibly responsive, often replying '
                   'within minutes, which makes it feel like I have a tech '
                   'team right beside me — something I really appreciate. I’ve '
                   'been running the EA from May 26th to May 29th, and it '
                   'delivered a 2% profit within just four days using the '
                   'default risk settings. It doesn’t use a martingale '
                   'strategy, but it does attempt to average price levels in a '
                   'controlled manner. I especially like that it only executes '
                   'trades when specific market conditions are met, aiming for '
                   'higher probability setups. So far, the performance has '
                   'been solid and feels safe. Highly recommended, and based '
                   'on these results, I plan to explore more of their '
                   'products. 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:31 # Thank you so much for the thoughtful '
                   'and detailed feedback, Leo! 🙏 I’m really glad to hear that '
                   'you appreciate the way Quantum Queen handles trades — '
                   'focused, careful, and without martingale risk. I’ve spent '
                   'a lot of time fine-tuning the strategy to be both smart '
                   'and safe, and it’s great to know it’s performing well for '
                   'you. I’m also always happy to help, so don’t hesitate to '
                   'reach out anytime. Looking forward to having you explore '
                   'the rest of the Quantum family — there’s more good stuff '
                   'waiting!',
 'review_datetime': '2025.05.30 08:56',
 'review_has_answer': False,
 'review_id': '38237707',
 'reviewer_id': 'chasuhun23',
 'reviewer_name': 'Leo',
 'reviewer_score': 56,
 'scraped_at': '2025-06-13T15:59:45.393985',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '24 Michael Schnitzer 2025.05.29 17:57 # I can highly '
                   'recommend the Queen. You get an immediate response to any '
                   'kind of question, and the system truly works. Thank you, '
                   'Bogdan! 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:29 # Thank you so much for the wonderful '
                   'feedback! I’m really happy to hear that Quantum Queen is '
                   'working well for you and that you feel supported — I’m '
                   'always here to help whenever you need. Wishing you '
                   'continued success and steady profits!',
 'review_datetime': '2025.05.29 17:57',
 'review_has_answer': False,
 'review_id': '38508546',
 'reviewer_id': 'm_schnitzer',
 'reviewer_name': 'Michael Schnitzer',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:59:45.396875',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bought the EA last week and already made about 4% in just '
                   'one week! Really happy with the performance so far. The '
                   'documentation provided is very detailed and informative — '
                   'it made setup and understanding the strategy very easy. '
                   'Looking forward to seeing more great results. 34750 Reply '
                   'from developer Bogdan Ion Puscasu 2025.06.05 14:37 # '
                   "That's fantastic to hear! I'm really glad the setup "
                   "process went smoothly and that you're already seeing solid "
                   'results in just one week — 4% is a great start! Thanks for '
                   'pointing out the documentation too, I put a lot of effort '
                   'into making it as clear and helpful as possible. Wishing '
                   'you continued success, and if you ever need anything, '
                   'don’t hesitate to reach out.',
 'review_datetime': '2025.06.02 13:36',
 'review_has_answer': False,
 'review_id': '38600643',
 'reviewer_id': 'nasoo',
 'reviewer_name': 'Nasoo',
 'reviewer_score': 785,
 'scraped_at': '2025-06-13T15:59:45.399986',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'I am very satisfied with the results from the 1st week, '
                   'its the same as in the backtests. I fully recommend it. '
                   '34750 Reply from developer Bogdan Ion Puscasu 2025.06.05 '
                   "14:35 # Thank you so much for the great feedback! I'm "
                   'really glad to hear that your first week with Quantum '
                   "Queen matches the backtests — that's always a great sign. "
                   "It means a lot to know you're satisfied and confident in "
                   'the EA. Wishing you continued success and steady profits '
                   "ahead! If you ever need help or have questions, I'm here.",
 'review_datetime': '2025.06.01 15:52',
 'review_has_answer': False,
 'review_id': '38580848',
 'reviewer_id': 'francesc00',
 'reviewer_name': 'Francesc00',
 'reviewer_score': 34,
 'scraped_at': '2025-06-13T15:59:45.402661',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': "User didn't leave any comment to the rating",
 'review_datetime': '2025.05.31 06:14',
 'review_has_answer': False,
 'review_id': '38668358',
 'reviewer_id': 'schnitzler88',
 'reviewer_name': 'Extoflex',
 'reviewer_score': 328,
 'scraped_at': '2025-06-13T15:59:45.406229',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '40 Lucas Ramos 2025.05.30 09:43 # O Bogdan é um Trader '
                   'extremamente responsável e simpático e oferece suporte '
                   'rápido, a Quantum Queen é uma ótima EA que já me rendeu '
                   'bons lucros com baixos riscos, recomendo fortemente o uso '
                   'dessa EA para Traders responsáveis e que queiram fazer seu '
                   'dinheiro render sem muito esforço 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:33 # Thank you '
                   "so much for your kind words and trust, Lucas! 🙏 I'm really "
                   'happy to hear that Quantum Queen is delivering good '
                   'profits with low risk for you — exactly what I designed it '
                   'for. I truly appreciate your support and recommendation, '
                   "and I'm always here if you need help or have any "
                   'questions. Wishing you continued success and steady growth '
                   "— let's keep making the most out of every trade!",
 'review_datetime': '2025.05.30 09:43',
 'review_has_answer': False,
 'review_id': '38537336',
 'reviewer_id': 'lucasramoss',
 'reviewer_name': 'Lucas Ramos',
 'reviewer_score': 40,
 'scraped_at': '2025-06-13T15:59:45.409054',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bogdan has been incredibly responsive, often replying '
                   'within minutes, which makes it feel like I have a tech '
                   'team right beside me — something I really appreciate. I’ve '
                   'been running the EA from May 26th to May 29th, and it '
                   'delivered a 2% profit within just four days using the '
                   'default risk settings. It doesn’t use a martingale '
                   'strategy, but it does attempt to average price levels in a '
                   'controlled manner. I especially like that it only executes '
                   'trades when specific market conditions are met, aiming for '
                   'higher probability setups. So far, the performance has '
                   'been solid and feels safe. Highly recommended, and based '
                   'on these results, I plan to explore more of their '
                   'products. 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:31 # Thank you so much for the thoughtful '
                   'and detailed feedback, Leo! 🙏 I’m really glad to hear that '
                   'you appreciate the way Quantum Queen handles trades — '
                   'focused, careful, and without martingale risk. I’ve spent '
                   'a lot of time fine-tuning the strategy to be both smart '
                   'and safe, and it’s great to know it’s performing well for '
                   'you. I’m also always happy to help, so don’t hesitate to '
                   'reach out anytime. Looking forward to having you explore '
                   'the rest of the Quantum family — there’s more good stuff '
                   'waiting!',
 'review_datetime': '2025.05.30 08:56',
 'review_has_answer': False,
 'review_id': '38237707',
 'reviewer_id': 'chasuhun23',
 'reviewer_name': 'Leo',
 'reviewer_score': 56,
 'scraped_at': '2025-06-13T15:59:45.429171',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '24 Michael Schnitzer 2025.05.29 17:57 # I can highly '
                   'recommend the Queen. You get an immediate response to any '
                   'kind of question, and the system truly works. Thank you, '
                   'Bogdan! 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:29 # Thank you so much for the wonderful '
                   'feedback! I’m really happy to hear that Quantum Queen is '
                   'working well for you and that you feel supported — I’m '
                   'always here to help whenever you need. Wishing you '
                   'continued success and steady profits!',
 'review_datetime': '2025.05.29 17:57',
 'review_has_answer': False,
 'review_id': '38508546',
 'reviewer_id': 'm_schnitzer',
 'reviewer_name': 'Michael Schnitzer',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:59:45.431688',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '35 Steels is 2025.05.29 12:33 # "It is a great system and '
                   'a profitable, thank you Bogdan! 34750 Reply from developer '
                   'Bogdan Ion Puscasu 2025.06.05 14:28 # Thank you so much '
                   'for your kind words! I’m really glad to hear you’re seeing '
                   'great results with the system. That’s exactly what I aimed '
                   'for when building Quantum Queen. Your support means a lot '
                   '— thank you!',
 'review_datetime': '2025.05.29 12:33',
 'review_has_answer': False,
 'review_id': '38517999',
 'reviewer_id': 'steelis',
 'reviewer_name': 'Steels is',
 'reviewer_score': 35,
 'scraped_at': '2025-06-13T15:59:45.435593',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '40 Lucas Ramos 2025.05.30 09:43 # O Bogdan é um Trader '
                   'extremamente responsável e simpático e oferece suporte '
                   'rápido, a Quantum Queen é uma ótima EA que já me rendeu '
                   'bons lucros com baixos riscos, recomendo fortemente o uso '
                   'dessa EA para Traders responsáveis e que queiram fazer seu '
                   'dinheiro render sem muito esforço 34750 Reply from '
                   'developer Bogdan Ion Puscasu 2025.06.05 14:33 # Thank you '
                   "so much for your kind words and trust, Lucas! 🙏 I'm really "
                   'happy to hear that Quantum Queen is delivering good '
                   'profits with low risk for you — exactly what I designed it '
                   'for. I truly appreciate your support and recommendation, '
                   "and I'm always here if you need help or have any "
                   'questions. Wishing you continued success and steady growth '
                   "— let's keep making the most out of every trade!",
 'review_datetime': '2025.05.30 09:43',
 'review_has_answer': False,
 'review_id': '38537336',
 'reviewer_id': 'lucasramoss',
 'reviewer_name': 'Lucas Ramos',
 'reviewer_score': 40,
 'scraped_at': '2025-06-13T15:59:45.440949',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bogdan has been incredibly responsive, often replying '
                   'within minutes, which makes it feel like I have a tech '
                   'team right beside me — something I really appreciate. I’ve '
                   'been running the EA from May 26th to May 29th, and it '
                   'delivered a 2% profit within just four days using the '
                   'default risk settings. It doesn’t use a martingale '
                   'strategy, but it does attempt to average price levels in a '
                   'controlled manner. I especially like that it only executes '
                   'trades when specific market conditions are met, aiming for '
                   'higher probability setups. So far, the performance has '
                   'been solid and feels safe. Highly recommended, and based '
                   'on these results, I plan to explore more of their '
                   'products. 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:31 # Thank you so much for the thoughtful '
                   'and detailed feedback, Leo! 🙏 I’m really glad to hear that '
                   'you appreciate the way Quantum Queen handles trades — '
                   'focused, careful, and without martingale risk. I’ve spent '
                   'a lot of time fine-tuning the strategy to be both smart '
                   'and safe, and it’s great to know it’s performing well for '
                   'you. I’m also always happy to help, so don’t hesitate to '
                   'reach out anytime. Looking forward to having you explore '
                   'the rest of the Quantum family — there’s more good stuff '
                   'waiting!',
 'review_datetime': '2025.05.30 08:56',
 'review_has_answer': False,
 'review_id': '38237707',
 'reviewer_id': 'chasuhun23',
 'reviewer_name': 'Leo',
 'reviewer_score': 56,
 'scraped_at': '2025-06-13T15:59:45.445458',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '24 Michael Schnitzer 2025.05.29 17:57 # I can highly '
                   'recommend the Queen. You get an immediate response to any '
                   'kind of question, and the system truly works. Thank you, '
                   'Bogdan! 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:29 # Thank you so much for the wonderful '
                   'feedback! I’m really happy to hear that Quantum Queen is '
                   'working well for you and that you feel supported — I’m '
                   'always here to help whenever you need. Wishing you '
                   'continued success and steady profits!',
 'review_datetime': '2025.05.29 17:57',
 'review_has_answer': False,
 'review_id': '38508546',
 'reviewer_id': 'm_schnitzer',
 'reviewer_name': 'Michael Schnitzer',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:59:45.448415',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bogdan has been incredibly responsive, often replying '
                   'within minutes, which makes it feel like I have a tech '
                   'team right beside me — something I really appreciate. I’ve '
                   'been running the EA from May 26th to May 29th, and it '
                   'delivered a 2% profit within just four days using the '
                   'default risk settings. It doesn’t use a martingale '
                   'strategy, but it does attempt to average price levels in a '
                   'controlled manner. I especially like that it only executes '
                   'trades when specific market conditions are met, aiming for '
                   'higher probability setups. So far, the performance has '
                   'been solid and feels safe. Highly recommended, and based '
                   'on these results, I plan to explore more of their '
                   'products. 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:31 # Thank you so much for the thoughtful '
                   'and detailed feedback, Leo! 🙏 I’m really glad to hear that '
                   'you appreciate the way Quantum Queen handles trades — '
                   'focused, careful, and without martingale risk. I’ve spent '
                   'a lot of time fine-tuning the strategy to be both smart '
                   'and safe, and it’s great to know it’s performing well for '
                   'you. I’m also always happy to help, so don’t hesitate to '
                   'reach out anytime. Looking forward to having you explore '
                   'the rest of the Quantum family — there’s more good stuff '
                   'waiting!',
 'review_datetime': '2025.05.30 08:56',
 'review_has_answer': False,
 'review_id': '38237707',
 'reviewer_id': 'chasuhun23',
 'reviewer_name': 'Leo',
 'reviewer_score': 56,
 'scraped_at': '2025-06-13T15:59:45.451641',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '24 Michael Schnitzer 2025.05.29 17:57 # I can highly '
                   'recommend the Queen. You get an immediate response to any '
                   'kind of question, and the system truly works. Thank you, '
                   'Bogdan! 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:29 # Thank you so much for the wonderful '
                   'feedback! I’m really happy to hear that Quantum Queen is '
                   'working well for you and that you feel supported — I’m '
                   'always here to help whenever you need. Wishing you '
                   'continued success and steady profits!',
 'review_datetime': '2025.05.29 17:57',
 'review_has_answer': False,
 'review_id': '38508546',
 'reviewer_id': 'm_schnitzer',
 'reviewer_name': 'Michael Schnitzer',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:59:45.454794',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '35 Steels is 2025.05.29 12:33 # "It is a great system and '
                   'a profitable, thank you Bogdan! 34750 Reply from developer '
                   'Bogdan Ion Puscasu 2025.06.05 14:28 # Thank you so much '
                   'for your kind words! I’m really glad to hear you’re seeing '
                   'great results with the system. That’s exactly what I aimed '
                   'for when building Quantum Queen. Your support means a lot '
                   '— thank you!',
 'review_datetime': '2025.05.29 12:33',
 'review_has_answer': False,
 'review_id': '38517999',
 'reviewer_id': 'steelis',
 'reviewer_name': 'Steels is',
 'reviewer_score': 35,
 'scraped_at': '2025-06-13T15:59:45.457846',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '24 Michael Schnitzer 2025.05.29 17:57 # I can highly '
                   'recommend the Queen. You get an immediate response to any '
                   'kind of question, and the system truly works. Thank you, '
                   'Bogdan! 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:29 # Thank you so much for the wonderful '
                   'feedback! I’m really happy to hear that Quantum Queen is '
                   'working well for you and that you feel supported — I’m '
                   'always here to help whenever you need. Wishing you '
                   'continued success and steady profits!',
 'review_datetime': '2025.05.29 17:57',
 'review_has_answer': False,
 'review_id': '38508546',
 'reviewer_id': 'm_schnitzer',
 'reviewer_name': 'Michael Schnitzer',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:59:45.461316',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '35 Steels is 2025.05.29 12:33 # "It is a great system and '
                   'a profitable, thank you Bogdan! 34750 Reply from developer '
                   'Bogdan Ion Puscasu 2025.06.05 14:28 # Thank you so much '
                   'for your kind words! I’m really glad to hear you’re seeing '
                   'great results with the system. That’s exactly what I aimed '
                   'for when building Quantum Queen. Your support means a lot '
                   '— thank you!',
 'review_datetime': '2025.05.29 12:33',
 'review_has_answer': False,
 'review_id': '38517999',
 'reviewer_id': 'steelis',
 'reviewer_name': 'Steels is',
 'reviewer_score': 35,
 'scraped_at': '2025-06-13T15:59:45.464953',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bogdan has been incredibly responsive, often replying '
                   'within minutes, which makes it feel like I have a tech '
                   'team right beside me — something I really appreciate. I’ve '
                   'been running the EA from May 26th to May 29th, and it '
                   'delivered a 2% profit within just four days using the '
                   'default risk settings. It doesn’t use a martingale '
                   'strategy, but it does attempt to average price levels in a '
                   'controlled manner. I especially like that it only executes '
                   'trades when specific market conditions are met, aiming for '
                   'higher probability setups. So far, the performance has '
                   'been solid and feels safe. Highly recommended, and based '
                   'on these results, I plan to explore more of their '
                   'products. 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:31 # Thank you so much for the thoughtful '
                   'and detailed feedback, Leo! 🙏 I’m really glad to hear that '
                   'you appreciate the way Quantum Queen handles trades — '
                   'focused, careful, and without martingale risk. I’ve spent '
                   'a lot of time fine-tuning the strategy to be both smart '
                   'and safe, and it’s great to know it’s performing well for '
                   'you. I’m also always happy to help, so don’t hesitate to '
                   'reach out anytime. Looking forward to having you explore '
                   'the rest of the Quantum family — there’s more good stuff '
                   'waiting!',
 'review_datetime': '2025.05.30 08:56',
 'review_has_answer': False,
 'review_id': '38237707',
 'reviewer_id': 'chasuhun23',
 'reviewer_name': 'Leo',
 'reviewer_score': 56,
 'scraped_at': '2025-06-13T15:59:45.470776',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '24 Michael Schnitzer 2025.05.29 17:57 # I can highly '
                   'recommend the Queen. You get an immediate response to any '
                   'kind of question, and the system truly works. Thank you, '
                   'Bogdan! 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:29 # Thank you so much for the wonderful '
                   'feedback! I’m really happy to hear that Quantum Queen is '
                   'working well for you and that you feel supported — I’m '
                   'always here to help whenever you need. Wishing you '
                   'continued success and steady profits!',
 'review_datetime': '2025.05.29 17:57',
 'review_has_answer': False,
 'review_id': '38508546',
 'reviewer_id': 'm_schnitzer',
 'reviewer_name': 'Michael Schnitzer',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:59:45.477244',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '24 Michael Schnitzer 2025.05.29 17:57 # I can highly '
                   'recommend the Queen. You get an immediate response to any '
                   'kind of question, and the system truly works. Thank you, '
                   'Bogdan! 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:29 # Thank you so much for the wonderful '
                   'feedback! I’m really happy to hear that Quantum Queen is '
                   'working well for you and that you feel supported — I’m '
                   'always here to help whenever you need. Wishing you '
                   'continued success and steady profits!',
 'review_datetime': '2025.05.29 17:57',
 'review_has_answer': False,
 'review_id': '38508546',
 'reviewer_id': 'm_schnitzer',
 'reviewer_name': 'Michael Schnitzer',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:59:45.480506',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '35 Steels is 2025.05.29 12:33 # "It is a great system and '
                   'a profitable, thank you Bogdan! 34750 Reply from developer '
                   'Bogdan Ion Puscasu 2025.06.05 14:28 # Thank you so much '
                   'for your kind words! I’m really glad to hear you’re seeing '
                   'great results with the system. That’s exactly what I aimed '
                   'for when building Quantum Queen. Your support means a lot '
                   '— thank you!',
 'review_datetime': '2025.05.29 12:33',
 'review_has_answer': False,
 'review_id': '38517999',
 'reviewer_id': 'steelis',
 'reviewer_name': 'Steels is',
 'reviewer_score': 35,
 'scraped_at': '2025-06-13T15:59:45.483723',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '35 Steels is 2025.05.29 12:33 # "It is a great system and '
                   'a profitable, thank you Bogdan! 34750 Reply from developer '
                   'Bogdan Ion Puscasu 2025.06.05 14:28 # Thank you so much '
                   'for your kind words! I’m really glad to hear you’re seeing '
                   'great results with the system. That’s exactly what I aimed '
                   'for when building Quantum Queen. Your support means a lot '
                   '— thank you!',
 'review_datetime': '2025.05.29 12:33',
 'review_has_answer': False,
 'review_id': '38517999',
 'reviewer_id': 'steelis',
 'reviewer_name': 'Steels is',
 'reviewer_score': 35,
 'scraped_at': '2025-06-13T15:59:45.487470',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': 'Bogdan has been incredibly responsive, often replying '
                   'within minutes, which makes it feel like I have a tech '
                   'team right beside me — something I really appreciate. I’ve '
                   'been running the EA from May 26th to May 29th, and it '
                   'delivered a 2% profit within just four days using the '
                   'default risk settings. It doesn’t use a martingale '
                   'strategy, but it does attempt to average price levels in a '
                   'controlled manner. I especially like that it only executes '
                   'trades when specific market conditions are met, aiming for '
                   'higher probability setups. So far, the performance has '
                   'been solid and feels safe. Highly recommended, and based '
                   'on these results, I plan to explore more of their '
                   'products. 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:31 # Thank you so much for the thoughtful '
                   'and detailed feedback, Leo! 🙏 I’m really glad to hear that '
                   'you appreciate the way Quantum Queen handles trades — '
                   'focused, careful, and without martingale risk. I’ve spent '
                   'a lot of time fine-tuning the strategy to be both smart '
                   'and safe, and it’s great to know it’s performing well for '
                   'you. I’m also always happy to help, so don’t hesitate to '
                   'reach out anytime. Looking forward to having you explore '
                   'the rest of the Quantum family — there’s more good stuff '
                   'waiting!',
 'review_datetime': '2025.05.30 08:56',
 'review_has_answer': False,
 'review_id': '38237707',
 'reviewer_id': 'chasuhun23',
 'reviewer_name': 'Leo',
 'reviewer_score': 56,
 'scraped_at': '2025-06-13T15:59:45.493854',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '24 Michael Schnitzer 2025.05.29 17:57 # I can highly '
                   'recommend the Queen. You get an immediate response to any '
                   'kind of question, and the system truly works. Thank you, '
                   'Bogdan! 34750 Reply from developer Bogdan Ion Puscasu '
                   '2025.06.05 14:29 # Thank you so much for the wonderful '
                   'feedback! I’m really happy to hear that Quantum Queen is '
                   'working well for you and that you feel supported — I’m '
                   'always here to help whenever you need. Wishing you '
                   'continued success and steady profits!',
 'review_datetime': '2025.05.29 17:57',
 'review_has_answer': False,
 'review_id': '38508546',
 'reviewer_id': 'm_schnitzer',
 'reviewer_name': 'Michael Schnitzer',
 'reviewer_score': 24,
 'scraped_at': '2025-06-13T15:59:45.497182',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '35 Steels is 2025.05.29 12:33 # "It is a great system and '
                   'a profitable, thank you Bogdan! 34750 Reply from developer '
                   'Bogdan Ion Puscasu 2025.06.05 14:28 # Thank you so much '
                   'for your kind words! I’m really glad to hear you’re seeing '
                   'great results with the system. That’s exactly what I aimed '
                   'for when building Quantum Queen. Your support means a lot '
                   '— thank you!',
 'review_datetime': '2025.05.29 12:33',
 'review_has_answer': False,
 'review_id': '38517999',
 'reviewer_id': 'steelis',
 'reviewer_name': 'Steels is',
 'reviewer_score': 35,
 'scraped_at': '2025-06-13T15:59:45.499916',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '35 Steels is 2025.05.29 12:33 # "It is a great system and '
                   'a profitable, thank you Bogdan! 34750 Reply from developer '
                   'Bogdan Ion Puscasu 2025.06.05 14:28 # Thank you so much '
                   'for your kind words! I’m really glad to hear you’re seeing '
                   'great results with the system. That’s exactly what I aimed '
                   'for when building Quantum Queen. Your support means a lot '
                   '— thank you!',
 'review_datetime': '2025.05.29 12:33',
 'review_has_answer': False,
 'review_id': '38517999',
 'reviewer_id': 'steelis',
 'reviewer_name': 'Steels is',
 'reviewer_score': 35,
 'scraped_at': '2025-06-13T15:59:45.521388',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: review_https://www.mql5.com/en/market/product/118805
{'product_id': '118805',
 'rating_description_quality': 5.0,
 'rating_reliability': 5.0,
 'rating_support': 5.0,
 'review_answer': '',
 'review_content': '35 Steels is 2025.05.29 12:33 # "It is a great system and '
                   'a profitable, thank you Bogdan! 34750 Reply from developer '
                   'Bogdan Ion Puscasu 2025.06.05 14:28 # Thank you so much '
                   'for your kind words! I’m really glad to hear you’re seeing '
                   'great results with the system. That’s exactly what I aimed '
                   'for when building Quantum Queen. Your support means a lot '
                   '— thank you!',
 'review_datetime': '2025.05.29 12:33',
 'review_has_answer': False,
 'review_id': '38517999',
 'reviewer_id': 'steelis',
 'reviewer_name': 'Steels is',
 'reviewer_score': 35,
 'scraped_at': '2025-06-13T15:59:45.524782',
 'timestamp_extraction_review': '2025-06-13 15:59',
 'url': 'https://www.mql5.com/en/market/product/118805'}
2025-06-13 15:59:45 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:59:45 [review] INFO: Exported 1 items to output/review_20250613_155944.json
2025-06-13 15:59:45 [scrapy.extensions.feedexport] INFO: Stored json feed (1 items) in: review_debug.json
2025-06-13 15:59:45 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 3238,
 'downloader/request_count': 9,
 'downloader/request_method_count/GET': 9,
 'downloader/response_bytes': 664803,
 'downloader/response_count': 9,
 'downloader/response_status_count/200': 9,
 'elapsed_time_seconds': 1.055291,
 'feedexport/success_count/FileFeedStorage': 1,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 59, 45, 541471, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 9,
 'httpcompression/response_bytes': 2752290,
 'httpcompression/response_count': 9,
 'item_dropped_count': 159,
 'item_dropped_reasons_count/DropItem': 159,
 'item_scraped_count': 1,
 'items_per_minute': 60.0,
 'log_count/DEBUG': 14,
 'log_count/INFO': 20,
 'log_count/WARNING': 163,
 'memusage/max': 75669504,
 'memusage/startup': 75669504,
 'request_depth_max': 1,
 'response_received_count': 9,
 'responses_per_minute': 540.0,
 'scheduler/dequeued': 9,
 'scheduler/dequeued/memory': 9,
 'scheduler/enqueued': 9,
 'scheduler/enqueued/memory': 9,
 'start_time': datetime.datetime(2025, 6, 13, 13, 59, 44, 486180, tzinfo=datetime.timezone.utc)}
2025-06-13 15:59:45 [scrapy.core.engine] INFO: Spider closed (finished)
