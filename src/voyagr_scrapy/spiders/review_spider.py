import scrapy
from datetime import datetime
from voyagr_scrapy.items import ReviewItem
import re
import math
import html


class ReviewSpider(scrapy.Spider):
    name = 'review'
    allowed_domains = ['mql5.com']
    start_urls = []

    def __init__(self, *args, **kwargs):
        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__(*args, **kwargs)
        self.start_urls = kwargs.get('start_urls', '').split(',') if kwargs.get('start_urls') else []
        if kwargs.get('allowed_domains'):
            self.allowed_domains = kwargs.get('allowed_domains').split(',')

    def parse(self, response):
        """
        Parse the product page and navigate to reviews tab
        """
        # Extract product ID from URL
        product_id = self.extract_product_id(response)
        if not product_id:
            self.logger.error(f"Could not extract product ID from URL: {response.url}")
            return

        # Get total review count to calculate pages
        review_count = self.extract_review_count(response)
        if not review_count:
            self.logger.warning(f"No reviews found for product {product_id}")
            return

        # Calculate total pages (20 reviews per page)
        total_pages = math.ceil(review_count / 20)
        self.logger.info(f"Product {product_id} has {review_count} reviews across {total_pages} pages")

        # First, parse reviews from the current page (which might already be showing reviews)
        self.parse_reviews_from_current_page(response, product_id)

        # Generate URLs for all review pages if there are multiple pages
        if total_pages > 1:
            base_url = response.url.split('?')[0]  # Remove query parameters

            for page in range(1, total_pages + 1):
                review_url = f"{base_url}#!tab=reviews"
                if page > 1:
                    review_url += f"&page={page}"

                yield scrapy.Request(
                    url=review_url,
                    callback=self.parse_reviews_page,
                    meta={
                        'product_id': product_id,
                        'page': page,
                        'total_pages': total_pages
                    },
                    dont_filter=True  # Allow duplicate URLs with different fragments
                )
        else:
            # If only one page, parse reviews from current response
            for review_item in self.parse_reviews_from_current_page(response, product_id):
                yield review_item

    def parse_reviews_from_current_page(self, response, product_id):
        """
        Parse reviews from the current page response
        """
        self.logger.info(f"Parsing reviews from current page for product {product_id}")

        # Try multiple approaches to find review containers
        review_containers = []

        # Approach 1: Look for divs with review IDs
        containers_1 = response.css('div[id*="review_"]:not([id*="reply_"])')
        review_containers.extend(containers_1)

        # Approach 2: Look for review structures based on the HTML pattern we saw
        # Look for img tags with reviewer avatars, then get their parent containers
        avatar_containers = response.css('img[alt*="avatar"], img[src*="avatar"]')
        for avatar in avatar_containers:
            # Get the parent container that likely contains the full review
            parent = avatar.xpath('./ancestor::*[contains(@class, "review") or contains(@id, "review") or position() <= 3][1]')
            if parent:
                review_containers.extend(parent)

        # Approach 3: Look for patterns with reviewer names and dates
        reviewer_links = response.css('a[href*="/users/"]')
        for link in reviewer_links:
            # Get container that has both reviewer link and review content
            container = link.xpath('./ancestor::*[contains(text(), "2025.") or contains(text(), "2024.")][1]')
            if container:
                review_containers.extend(container)

        # Remove duplicates
        unique_containers = []
        seen_ids = set()
        for container in review_containers:
            container_id = container.css('::attr(id)').get()
            if container_id and container_id not in seen_ids:
                unique_containers.append(container)
                seen_ids.add(container_id)
            elif not container_id:
                # For containers without IDs, check if they have review content
                text_content = ' '.join(container.css('*::text').getall())
                if len(text_content) > 50 and any(year in text_content for year in ['2025', '2024']):
                    unique_containers.append(container)

        self.logger.info(f"Found {len(unique_containers)} potential review containers")

        for container in unique_containers:
            review_item = self.extract_review_data(container, response, product_id)
            if review_item:
                yield review_item

    def parse_reviews_page(self, response):
        """
        Parse individual review page and extract all reviews
        """
        product_id = response.meta['product_id']
        page = response.meta['page']

        self.logger.info(f"Parsing reviews page {page} for product {product_id}")

        # Find all review containers - avoid reply containers in main extraction
        review_containers = response.css('div[id*="review_"]:not([id*="reply_"])')

        for container in review_containers:
            review_item = self.extract_review_data(container, response, product_id)
            if review_item:
                yield review_item

    def extract_product_id(self, response):
        """Extract product ID from URL"""
        url_match = re.search(r'/product/(\d+)', response.url)
        if url_match:
            return url_match.group(1)
        return None

    def extract_review_count(self, response):
        """Extract total review count from the page"""
        # Look for "Reviews (153)" pattern in the page
        all_text = ' '.join(response.css('*::text').getall())
        review_match = re.search(r'Reviews\s*\((\d+)\)', all_text)
        if review_match:
            return int(review_match.group(1))

        # Alternative: look for review count in tab text
        review_tab = response.css('*:contains("Reviews")::text').getall()
        for text in review_tab:
            match = re.search(r'Reviews\s*\((\d+)\)', text)
            if match:
                return int(match.group(1))

        return None

    def extract_review_data(self, container, response, product_id):
        """Extract data from individual review container"""
        try:
            # Extract review ID - try multiple approaches
            review_id = self.extract_review_id(container)
            if not review_id:
                # Generate a temporary ID based on content hash
                content_text = ' '.join(container.css('*::text').getall())
                review_id = str(hash(content_text[:100]))

            # Extract reviewer information
            reviewer_name = self.extract_reviewer_name(container)
            reviewer_id = self.extract_reviewer_id(container)
            reviewer_score = self.extract_reviewer_score(container)

            # Skip if we can't find basic reviewer info
            if not reviewer_name and not reviewer_id:
                return None

            # Extract review content
            review_content = self.extract_review_content(container)

            # Extract datetime
            review_datetime = self.extract_review_datetime(container)

            # Extract detailed ratings - try to get from the overall page context
            rating_description_quality = 5.0  # Default based on the pattern we saw
            rating_reliability = 5.0
            rating_support = 5.0

            # Try to extract actual ratings
            ratings = self.extract_detailed_ratings(container, response)
            if ratings and len(ratings) >= 3:
                rating_description_quality = ratings[0]
                rating_reliability = ratings[1]
                rating_support = ratings[2]

            # Check if this review has an answer
            review_has_answer = self.check_has_answer(container, response, review_id)
            review_answer = self.extract_answer_content(container, response, review_id) if review_has_answer else ""

            # Create review item
            item = ReviewItem()
            item['review_id'] = review_id
            item['review_datetime'] = review_datetime
            item['reviewer_name'] = reviewer_name
            item['reviewer_id'] = reviewer_id
            item['reviewer_score'] = reviewer_score
            item['review_content'] = review_content
            item['review_has_answer'] = review_has_answer
            item['review_answer'] = review_answer
            item['rating_description_quality'] = rating_description_quality
            item['rating_reliability'] = rating_reliability
            item['rating_support'] = rating_support
            item['timestamp_extraction_review'] = datetime.now().strftime('%Y-%m-%d %H:%M')
            item['product_id'] = product_id
            item['url'] = response.url
            item['scraped_at'] = datetime.now().isoformat()

            return item

        except Exception as e:
            self.logger.error(f"Error extracting review data: {e}")
            return None

    def extract_review_id(self, container):
        """Extract review ID from container"""
        # Try to get ID from container attributes
        container_id = container.css('::attr(id)').get()
        if container_id:
            if container_id.startswith('review_'):
                return container_id.replace('review_', '')
            elif container_id.startswith('reply_'):
                return container_id.replace('reply_', '')

        # Try to find ID in links or anchors
        review_links = container.css('a[href*="#!tab=reviews&id=review_"]::attr(href)').getall()
        for link in review_links:
            match = re.search(r'review_(\d+)', link)
            if match:
                return match.group(1)

        return None

    def extract_reviewer_name(self, container):
        """Extract reviewer name"""
        # Try different selectors for reviewer name
        selectors = [
            'strong a::text',
            '.reviewer-name::text',
            'strong::text',
            'a[href*="/users/"]::text'
        ]

        for selector in selectors:
            name = container.css(selector).get()
            if name and name.strip():
                return name.strip()

        return None

    def extract_reviewer_id(self, container):
        """Extract reviewer ID from profile link"""
        # Look for user profile links
        profile_links = container.css('a[href*="/users/"]::attr(href)').getall()
        for link in profile_links:
            # Extract username from URL like "/en/users/username"
            match = re.search(r'/users/([^/]+)', link)
            if match:
                return match.group(1)

        return None

    def extract_reviewer_score(self, container):
        """Extract reviewer score/reputation"""
        # Look for score numbers near the reviewer name
        score_text = container.css('*::text').getall()
        for text in score_text:
            # Look for standalone numbers that could be scores
            if text.strip().isdigit():
                score = int(text.strip())
                # Reasonable score range
                if 1 <= score <= 100000:
                    return score

        return None

    def extract_review_content(self, container):
        """Extract review content, handling rich text and emojis"""
        # Look for the actual review content, avoiding metadata
        content_selectors = [
            'div:not([class*="meta"]):not([class*="header"]):not([class*="info"]) *::text',
            'p::text',
            '.review-text::text',
            '.content::text'
        ]

        # Try to find the main content area
        content_text = ""

        # First, try to get text from specific content areas
        for selector in content_selectors:
            texts = container.css(selector).getall()
            if texts:
                content_text = ' '.join(texts).strip()
                break

        # If no specific content found, get all text but filter out metadata
        if not content_text:
            all_texts = container.css('*::text').getall()
            # Filter out common metadata patterns
            filtered_texts = []
            for text in all_texts:
                text = text.strip()
                if text and not self._is_metadata_text(text):
                    filtered_texts.append(text)
            content_text = ' '.join(filtered_texts)

        # Clean up the content
        content_text = re.sub(r'\s+', ' ', content_text).strip()

        # Remove reviewer info from the beginning if present
        content_text = self._clean_reviewer_info(content_text)

        # Check for placeholder text indicating no comment
        placeholder_patterns = [
            r"User didn't leave any comment to the rating",
            r"User didn't leave any comment",
            r"No comment provided"
        ]

        for pattern in placeholder_patterns:
            if re.search(pattern, content_text, re.IGNORECASE):
                return "User didn't leave any comment to the rating"

        # Decode HTML entities to preserve emojis and special characters
        content_text = html.unescape(content_text)

        return content_text if content_text else None

    def _is_metadata_text(self, text):
        """Check if text is likely metadata (dates, usernames, scores)"""
        # Skip very short text
        if len(text) < 3:
            return True

        # Skip dates
        if re.match(r'\d{4}\.\d{2}\.\d{2}', text):
            return True

        # Skip standalone numbers (likely scores)
        if text.isdigit() and len(text) < 4:
            return True

        # Skip common UI elements
        ui_elements = ['#', 'Permanent link', 'Reply from developer']
        if any(element in text for element in ui_elements):
            return True

        return False

    def _clean_reviewer_info(self, content):
        """Remove reviewer name, score, and date from the beginning of content"""
        # Pattern to match: "19 Thanos 2025.06.12 15:58 #"
        pattern = r'^\d+\s+\w+\s+\d{4}\.\d{2}\.\d{2}\s+\d{2}:\d{2}\s*#?\s*'
        content = re.sub(pattern, '', content)

        # Also remove any remaining leading metadata
        pattern2 = r'^[^a-zA-Z]*[A-Z][a-z]+\s+\d{4}\.\d{2}\.\d{2}.*?#\s*'
        content = re.sub(pattern2, '', content)

        return content.strip()

    def extract_review_datetime(self, container):
        """Extract review datetime"""
        # Look for datetime patterns in the container
        datetime_patterns = [
            r'(\d{4}\.\d{2}\.\d{2}\s+\d{2}:\d{2})',  # 2025.06.08 02:25
            r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2})',    # 2025-06-08 02:25
        ]

        all_text = ' '.join(container.css('*::text').getall())

        for pattern in datetime_patterns:
            match = re.search(pattern, all_text)
            if match:
                return match.group(1)

        return None

    def extract_detailed_ratings(self, container, response):
        """Extract detailed ratings from JavaScript tooltip functions"""
        # Look for createRatingTooltip patterns in the container or nearby elements

        # 1. Check onmouseover attributes
        onmouseover_elements = container.css('*[onmouseover]')
        for element in onmouseover_elements:
            onmouseover_value = element.css('::attr(onmouseover)').get()
            if onmouseover_value and 'createRatingTooltip' in onmouseover_value:
                ratings = self._parse_rating_tooltip(onmouseover_value)
                if ratings:
                    return ratings

        # 2. Look in nearby script tags or page scripts
        script_tags = response.css('script::text').getall()
        for script in script_tags:
            tooltip_matches = re.findall(r'createRatingTooltip\([^,]+,\s*\[([^\]]+)\]\)', script)
            for values_str in tooltip_matches:
                try:
                    values = [float(x.strip()) for x in values_str.split(',')]
                    if len(values) >= 3:
                        return values[:3]  # Return first 3 ratings
                except (ValueError, IndexError):
                    continue

        return None

    def _parse_rating_tooltip(self, tooltip_text):
        """Parse rating values from createRatingTooltip function"""
        tooltip_match = re.search(r'createRatingTooltip\([^,]+,\s*\[([^\]]+)\]\)', tooltip_text)
        if tooltip_match:
            try:
                values_str = tooltip_match.group(1)
                values = [float(x.strip()) for x in values_str.split(',')]
                if len(values) >= 3:
                    return values[:3]
            except (ValueError, IndexError):
                pass
        return None

    def check_has_answer(self, container, response, review_id):
        """Check if review has a developer answer"""
        # Look for reply containers with matching review ID
        reply_selector = f'div[id="reply_{review_id}"]'
        reply_container = response.css(reply_selector)
        return bool(reply_container)

    def extract_answer_content(self, container, response, review_id):
        """Extract developer answer content"""
        reply_selector = f'div[id="reply_{review_id}"]'
        reply_container = response.css(reply_selector).first()

        if reply_container:
            # Extract text content from reply
            reply_texts = reply_container.css('*::text').getall()
            reply_content = ' '.join(reply_texts).strip()
            reply_content = re.sub(r'\s+', ' ', reply_content)
            return html.unescape(reply_content) if reply_content else ""

        return ""
