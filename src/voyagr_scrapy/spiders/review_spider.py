import scrapy
from datetime import datetime
from voyagr_scrapy.items import ReviewItem
import re
import math
import html


class ReviewSpider(scrapy.Spider):
    name = 'review'
    allowed_domains = ['mql5.com']
    start_urls = []

    def __init__(self, *args, **kwargs):
        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__(*args, **kwargs)
        self.start_urls = kwargs.get('start_urls', '').split(',') if kwargs.get('start_urls') else []
        if kwargs.get('allowed_domains'):
            self.allowed_domains = kwargs.get('allowed_domains').split(',')

    def parse(self, response):
        """
        Parse the product page and navigate to reviews tab
        """
        # Extract product ID from URL
        product_id = self.extract_product_id(response)
        if not product_id:
            self.logger.error(f"Could not extract product ID from URL: {response.url}")
            return

        # Get total review count to calculate pages
        review_count = self.extract_review_count(response)
        if not review_count:
            self.logger.warning(f"No reviews found for product {product_id}")
            return

        # Calculate total pages (20 reviews per page)
        total_pages = math.ceil(review_count / 20)
        self.logger.info(f"Product {product_id} has {review_count} reviews across {total_pages} pages")

        # Generate URLs for all review pages
        base_url = response.url.split('?')[0]  # Remove query parameters
        
        for page in range(1, total_pages + 1):
            review_url = f"{base_url}?source=Site+Market+MT5+Rating006#!tab=reviews&page={page}"
            yield scrapy.Request(
                url=review_url,
                callback=self.parse_reviews_page,
                meta={
                    'product_id': product_id,
                    'page': page,
                    'total_pages': total_pages
                }
            )

    def parse_reviews_page(self, response):
        """
        Parse individual review page and extract all reviews
        """
        product_id = response.meta['product_id']
        page = response.meta['page']
        
        self.logger.info(f"Parsing reviews page {page} for product {product_id}")

        # Find all review containers
        review_containers = response.css('div[id*="review_"], div[id*="reply_"]')
        
        for container in review_containers:
            review_item = self.extract_review_data(container, response, product_id)
            if review_item:
                yield review_item

    def extract_product_id(self, response):
        """Extract product ID from URL"""
        url_match = re.search(r'/product/(\d+)', response.url)
        if url_match:
            return url_match.group(1)
        return None

    def extract_review_count(self, response):
        """Extract total review count from the page"""
        # Look for "Reviews (153)" pattern in the page
        all_text = ' '.join(response.css('*::text').getall())
        review_match = re.search(r'Reviews\s*\((\d+)\)', all_text)
        if review_match:
            return int(review_match.group(1))
        
        # Alternative: look for review count in tab text
        review_tab = response.css('*:contains("Reviews")::text').getall()
        for text in review_tab:
            match = re.search(r'Reviews\s*\((\d+)\)', text)
            if match:
                return int(match.group(1))
        
        return None

    def extract_review_data(self, container, response, product_id):
        """Extract data from individual review container"""
        try:
            # Determine if this is a review or a reply
            container_id = container.css('::attr(id)').get()
            if not container_id:
                return None

            is_reply = container_id.startswith('reply_')
            
            # Extract review ID
            if is_reply:
                review_id = container_id.replace('reply_', '')
            else:
                review_id = container_id.replace('review_', '')

            # Extract reviewer information
            reviewer_name = self.extract_reviewer_name(container)
            reviewer_id = self.extract_reviewer_id(container)
            reviewer_score = self.extract_reviewer_score(container)

            # Extract review content
            review_content = self.extract_review_content(container)
            
            # Extract datetime
            review_datetime = self.extract_review_datetime(container)

            # Extract detailed ratings (only for main reviews, not replies)
            rating_description_quality = None
            rating_reliability = None
            rating_support = None
            
            if not is_reply:
                ratings = self.extract_detailed_ratings(container, response)
                if ratings and len(ratings) >= 3:
                    rating_description_quality = ratings[0]
                    rating_reliability = ratings[1]
                    rating_support = ratings[2]

            # Check if this review has an answer (look for reply containers)
            review_has_answer = self.check_has_answer(container, response, review_id)
            review_answer = self.extract_answer_content(container, response, review_id) if review_has_answer else ""

            # Create review item
            item = ReviewItem()
            item['review_id'] = review_id
            item['review_datetime'] = review_datetime
            item['reviewer_name'] = reviewer_name
            item['reviewer_id'] = reviewer_id
            item['reviewer_score'] = reviewer_score
            item['review_content'] = review_content
            item['review_has_answer'] = review_has_answer
            item['review_answer'] = review_answer
            item['rating_description_quality'] = rating_description_quality
            item['rating_reliability'] = rating_reliability
            item['rating_support'] = rating_support
            item['timestamp_extraction_review'] = datetime.now().strftime('%Y-%m-%d %H:%M')
            item['product_id'] = product_id
            item['url'] = response.url
            item['scraped_at'] = datetime.now().isoformat()

            return item

        except Exception as e:
            self.logger.error(f"Error extracting review data: {e}")
            return None

    def extract_reviewer_name(self, container):
        """Extract reviewer name"""
        # Try different selectors for reviewer name
        selectors = [
            'strong a::text',
            '.reviewer-name::text',
            'strong::text',
            'a[href*="/users/"]::text'
        ]
        
        for selector in selectors:
            name = container.css(selector).get()
            if name and name.strip():
                return name.strip()
        
        return None

    def extract_reviewer_id(self, container):
        """Extract reviewer ID from profile link"""
        # Look for user profile links
        profile_links = container.css('a[href*="/users/"]::attr(href)').getall()
        for link in profile_links:
            # Extract username from URL like "/en/users/username"
            match = re.search(r'/users/([^/]+)', link)
            if match:
                return match.group(1)
        
        return None

    def extract_reviewer_score(self, container):
        """Extract reviewer score/reputation"""
        # Look for score numbers near the reviewer name
        score_text = container.css('*::text').getall()
        for text in score_text:
            # Look for standalone numbers that could be scores
            if text.strip().isdigit():
                score = int(text.strip())
                # Reasonable score range
                if 1 <= score <= 100000:
                    return score
        
        return None

    def extract_review_content(self, container):
        """Extract review content, handling rich text and emojis"""
        # Get all text content from the review
        content_selectors = [
            '*::text'
        ]
        
        all_texts = []
        for selector in content_selectors:
            texts = container.css(selector).getall()
            all_texts.extend(texts)
        
        # Join and clean the content
        content = ' '.join(all_texts).strip()
        
        # Remove reviewer name and date from content if present
        # Clean up extra whitespace
        content = re.sub(r'\s+', ' ', content)
        
        # Check for placeholder text indicating no comment
        placeholder_patterns = [
            r"User didn't leave any comment to the rating",
            r"User didn't leave any comment",
            r"No comment provided"
        ]
        
        for pattern in placeholder_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return "User didn't leave any comment to the rating"
        
        # Decode HTML entities to preserve emojis and special characters
        content = html.unescape(content)
        
        return content if content else None

    def extract_review_datetime(self, container):
        """Extract review datetime"""
        # Look for datetime patterns in the container
        datetime_patterns = [
            r'(\d{4}\.\d{2}\.\d{2}\s+\d{2}:\d{2})',  # 2025.06.08 02:25
            r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2})',    # 2025-06-08 02:25
        ]
        
        all_text = ' '.join(container.css('*::text').getall())
        
        for pattern in datetime_patterns:
            match = re.search(pattern, all_text)
            if match:
                return match.group(1)
        
        return None

    def extract_detailed_ratings(self, container, response):
        """Extract detailed ratings from JavaScript tooltip functions"""
        # Look for createRatingTooltip patterns in the container or nearby elements
        
        # 1. Check onmouseover attributes
        onmouseover_elements = container.css('*[onmouseover]')
        for element in onmouseover_elements:
            onmouseover_value = element.css('::attr(onmouseover)').get()
            if onmouseover_value and 'createRatingTooltip' in onmouseover_value:
                ratings = self._parse_rating_tooltip(onmouseover_value)
                if ratings:
                    return ratings

        # 2. Look in nearby script tags or page scripts
        script_tags = response.css('script::text').getall()
        for script in script_tags:
            tooltip_matches = re.findall(r'createRatingTooltip\([^,]+,\s*\[([^\]]+)\]\)', script)
            for values_str in tooltip_matches:
                try:
                    values = [float(x.strip()) for x in values_str.split(',')]
                    if len(values) >= 3:
                        return values[:3]  # Return first 3 ratings
                except (ValueError, IndexError):
                    continue

        return None

    def _parse_rating_tooltip(self, tooltip_text):
        """Parse rating values from createRatingTooltip function"""
        tooltip_match = re.search(r'createRatingTooltip\([^,]+,\s*\[([^\]]+)\]\)', tooltip_text)
        if tooltip_match:
            try:
                values_str = tooltip_match.group(1)
                values = [float(x.strip()) for x in values_str.split(',')]
                if len(values) >= 3:
                    return values[:3]
            except (ValueError, IndexError):
                pass
        return None

    def check_has_answer(self, container, response, review_id):
        """Check if review has a developer answer"""
        # Look for reply containers with matching review ID
        reply_selector = f'div[id="reply_{review_id}"]'
        reply_container = response.css(reply_selector)
        return bool(reply_container)

    def extract_answer_content(self, container, response, review_id):
        """Extract developer answer content"""
        reply_selector = f'div[id="reply_{review_id}"]'
        reply_container = response.css(reply_selector).first()
        
        if reply_container:
            # Extract text content from reply
            reply_texts = reply_container.css('*::text').getall()
            reply_content = ' '.join(reply_texts).strip()
            reply_content = re.sub(r'\s+', ' ', reply_content)
            return html.unescape(reply_content) if reply_content else ""
        
        return ""
