import scrapy
from datetime import datetime
from voyagr_scrapy.items import ReviewItem
import re
import math
import html


class ReviewSpider(scrapy.Spider):
    name = 'review'
    allowed_domains = ['mql5.com']
    start_urls = []

    def __init__(self, *args, **kwargs):
        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__(*args, **kwargs)
        self.start_urls = kwargs.get('start_urls', '').split(',') if kwargs.get('start_urls') else []
        if kwargs.get('allowed_domains'):
            self.allowed_domains = kwargs.get('allowed_domains').split(',')

    def parse(self, response):
        """
        Parse the product page and use the API to get reviews
        """
        # Extract product ID from URL
        product_id = self.extract_product_id(response)
        if not product_id:
            self.logger.error(f"Could not extract product ID from URL: {response.url}")
            return

        # Get total review count to calculate pages
        review_count = self.extract_review_count(response)
        if not review_count:
            self.logger.warning(f"No reviews found for product {product_id}")
            return

        # Calculate total pages (20 reviews per page)
        total_pages = math.ceil(review_count / 20)
        self.logger.info(f"Product {product_id} has {review_count} reviews across {total_pages} pages")

        # Start with the first page only - pagination will be handled in parse_reviews_api
        api_url = f"https://www.mql5.com/market/product/{product_id}/reviews?filter=new&page=1"

        yield scrapy.Request(
            url=api_url,
            callback=self.parse_reviews_api,
            headers={
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            },
            meta={
                'product_id': product_id,
                'page': 1,
                'total_pages': total_pages
            },
            dont_filter=True
        )

    def parse_reviews_api(self, response):
        """
        Parse reviews from the API response, filtering for 2025+ reviews
        """
        product_id = response.meta['product_id']
        page = response.meta['page']
        total_pages = response.meta['total_pages']

        self.logger.info(f"Parsing reviews from API page {page}/{total_pages} for product {product_id}")

        # Find all review containers from the API response
        review_containers = response.css('div[id*="review_"]:not([id*="reply_"])')

        self.logger.info(f"Found {len(review_containers)} review containers on page {page}")

        reviews_from_2025 = 0
        oldest_review_year = None

        for container in review_containers:
            # First check the date to see if we should continue
            review_datetime = self.extract_review_datetime(container)
            if review_datetime:
                review_year = self.extract_year_from_datetime(review_datetime)
                if oldest_review_year is None or review_year < oldest_review_year:
                    oldest_review_year = review_year

                # Only process reviews from 2025 onwards
                if review_year >= 2025:
                    review_item = self.extract_review_data(container, response, product_id)
                    if review_item:
                        reviews_from_2025 += 1
                        yield review_item
                else:
                    self.logger.debug(f"Skipping review from {review_year} (before 2025)")
            else:
                # If we can't extract date, still process the review
                review_item = self.extract_review_data(container, response, product_id)
                if review_item:
                    yield review_item

        self.logger.info(f"Page {page}: Found {reviews_from_2025} reviews from 2025+, oldest review year: {oldest_review_year}")

        # If we found reviews older than 2025 on this page, we might want to continue
        # to next pages as reviews might not be perfectly chronological
        # But if ALL reviews on this page are from before 2025, we can consider stopping
        if oldest_review_year and oldest_review_year < 2025 and reviews_from_2025 == 0:
            self.logger.info(f"Page {page}: All reviews are from before 2025, but continuing to check remaining pages")

        # Continue to next page if we haven't reached the end
        if page < total_pages:
            next_page = page + 1
            api_url = f"https://www.mql5.com/market/product/{product_id}/reviews?filter=new&page={next_page}"

            yield scrapy.Request(
                url=api_url,
                callback=self.parse_reviews_api,
                headers={
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                },
                meta={
                    'product_id': product_id,
                    'page': next_page,
                    'total_pages': total_pages
                },
                dont_filter=True
            )

    def extract_product_id(self, response):
        """Extract product ID from URL"""
        url_match = re.search(r'/product/(\d+)', response.url)
        if url_match:
            return url_match.group(1)
        return None

    def extract_review_count(self, response):
        """Extract total review count from the page"""
        # Look for "Reviews (153)" pattern in the page
        all_text = ' '.join(response.css('*::text').getall())
        review_match = re.search(r'Reviews\s*\((\d+)\)', all_text)
        if review_match:
            return int(review_match.group(1))

        # Alternative: look for review count in tab text
        review_tab = response.css('*:contains("Reviews")::text').getall()
        for text in review_tab:
            match = re.search(r'Reviews\s*\((\d+)\)', text)
            if match:
                return int(match.group(1))

        return None

    def extract_year_from_datetime(self, datetime_str):
        """Extract year from datetime string"""
        if not datetime_str:
            return None

        # Look for year pattern in datetime string
        year_match = re.search(r'(\d{4})', datetime_str)
        if year_match:
            return int(year_match.group(1))

        return None

    def extract_review_data(self, container, response, product_id):
        """Extract data from individual review container"""
        try:
            # Extract review ID - try multiple approaches
            review_id = self.extract_review_id(container)
            if not review_id:
                # Generate a temporary ID based on content hash
                content_text = ' '.join(container.css('*::text').getall())
                review_id = str(hash(content_text[:100]))

            # Extract reviewer information
            reviewer_name = self.extract_reviewer_name(container)
            reviewer_id = self.extract_reviewer_id(container)
            reviewer_score = self.extract_reviewer_score(container)

            # Skip if we can't find basic reviewer info
            if not reviewer_name and not reviewer_id:
                return None

            # Extract review content
            review_content = self.extract_review_content(container)

            # Extract datetime
            review_datetime = self.extract_review_datetime(container)

            # Filter out reviews from before 2025
            if review_datetime:
                review_year = self.extract_year_from_datetime(review_datetime)
                if review_year and review_year < 2025:
                    self.logger.debug(f"Skipping review {review_id} from {review_year} (before 2025)")
                    return None

            # Extract detailed ratings - try to get from the overall page context
            rating_description_quality = 5.0  # Default based on the pattern we saw
            rating_reliability = 5.0
            rating_support = 5.0

            # Try to extract actual ratings
            ratings = self.extract_detailed_ratings(container, response)
            if ratings and len(ratings) >= 3:
                rating_description_quality = ratings[0]
                rating_reliability = ratings[1]
                rating_support = ratings[2]

            # Check if this review has an answer
            review_has_answer = self.check_has_answer(container, response, review_id)
            review_answer = self.extract_answer_content(container, response, review_id) if review_has_answer else ""

            # Create review item
            item = ReviewItem()
            item['review_id'] = review_id
            item['review_datetime'] = review_datetime
            item['reviewer_name'] = reviewer_name
            item['reviewer_id'] = reviewer_id
            item['reviewer_score'] = reviewer_score
            item['review_content'] = review_content
            item['review_has_answer'] = review_has_answer
            item['review_answer'] = review_answer
            item['rating_description_quality'] = rating_description_quality
            item['rating_reliability'] = rating_reliability
            item['rating_support'] = rating_support
            item['timestamp_extraction_review'] = datetime.now().strftime('%Y-%m-%d %H:%M')
            item['product_id'] = product_id
            item['url'] = response.url
            item['scraped_at'] = datetime.now().isoformat()

            return item

        except Exception as e:
            self.logger.error(f"Error extracting review data: {e}")
            return None

    def extract_review_id(self, container):
        """Extract review ID from container"""
        # Try to get ID from container attributes
        container_id = container.css('::attr(id)').get()
        if container_id:
            if container_id.startswith('review_'):
                return container_id.replace('review_', '')
            elif container_id.startswith('reply_'):
                return container_id.replace('reply_', '')

        # Try to find ID in links or anchors
        review_links = container.css('a[href*="#!tab=reviews&id=review_"]::attr(href)').getall()
        for link in review_links:
            match = re.search(r'review_(\d+)', link)
            if match:
                return match.group(1)

        return None

    def extract_reviewer_name(self, container):
        """Extract reviewer name"""
        # Look for the author link in the API response
        author_link = container.css('a.author::text').get()
        if author_link:
            return author_link.strip()

        # Fallback to other selectors
        selectors = [
            'strong a::text',
            '.reviewer-name::text',
            'strong::text',
            'a[href*="/users/"]::text'
        ]

        for selector in selectors:
            name = container.css(selector).get()
            if name and name.strip():
                return name.strip()

        return None

    def extract_reviewer_id(self, container):
        """Extract reviewer ID from profile link"""
        # Look for the author link in the API response
        author_link = container.css('a.author::attr(href)').get()
        if author_link:
            match = re.search(r'/users/([^/]+)', author_link)
            if match:
                return match.group(1)

        # Fallback: Look for any user profile links
        profile_links = container.css('a[href*="/users/"]::attr(href)').getall()
        for link in profile_links:
            match = re.search(r'/users/([^/]+)', link)
            if match:
                return match.group(1)

        return None

    def extract_reviewer_score(self, container):
        """Extract reviewer score/reputation"""
        # Look for the score in the user section with title="Note"
        score_element = container.css('.user span[title="Note"]::text').get()
        if score_element and score_element.strip().isdigit():
            return int(score_element.strip())

        # Fallback: Look for score numbers near the reviewer name
        score_text = container.css('*::text').getall()
        for text in score_text:
            if text.strip().isdigit():
                score = int(text.strip())
                # Reasonable score range
                if 1 <= score <= 100000:
                    return score

        return None

    def extract_review_content(self, container):
        """Extract review content, handling rich text and emojis"""
        # Look for the specific content block in the API response
        content_block = container.css('div[id*="reviewContentBlock_"] p')

        if content_block:
            # Get text from the paragraph
            content_text = content_block.css('::text').get()
            if content_text:
                content_text = content_text.strip()

                # Check for placeholder text indicating no comment (in different languages)
                placeholder_patterns = [
                    r"User didn't leave any comment to the rating",
                    r"User didn't leave any comment",
                    r"L'utilisateur n'a laissé aucun commentaire sur la note",
                    r"L'utilisateur n'a laissé aucun commentaire",
                    r"No comment provided"
                ]

                for pattern in placeholder_patterns:
                    if re.search(pattern, content_text, re.IGNORECASE):
                        return "User didn't leave any comment to the rating"

                # Decode HTML entities to preserve emojis and special characters
                content_text = html.unescape(content_text)
                return content_text

        # Fallback: try to extract from any paragraph in the container
        paragraphs = container.css('p::text').getall()
        if paragraphs:
            content_text = ' '.join(paragraphs).strip()
            content_text = html.unescape(content_text)
            return content_text if content_text else None

        return None

    def _is_metadata_text(self, text):
        """Check if text is likely metadata (dates, usernames, scores)"""
        # Skip very short text
        if len(text) < 3:
            return True

        # Skip dates
        if re.match(r'\d{4}\.\d{2}\.\d{2}', text):
            return True

        # Skip standalone numbers (likely scores)
        if text.isdigit() and len(text) < 4:
            return True

        # Skip common UI elements
        ui_elements = ['#', 'Permanent link', 'Reply from developer']
        if any(element in text for element in ui_elements):
            return True

        return False

    def _clean_reviewer_info(self, content):
        """Remove reviewer name, score, and date from the beginning of content"""
        # Pattern to match: "19 Thanos 2025.06.12 15:58 #"
        pattern = r'^\d+\s+\w+\s+\d{4}\.\d{2}\.\d{2}\s+\d{2}:\d{2}\s*#?\s*'
        content = re.sub(pattern, '', content)

        # Also remove any remaining leading metadata
        pattern2 = r'^[^a-zA-Z]*[A-Z][a-z]+\s+\d{4}\.\d{2}\.\d{2}.*?#\s*'
        content = re.sub(pattern2, '', content)

        return content.strip()

    def extract_review_datetime(self, container):
        """Extract review datetime"""
        # Look for the date in the API response format
        date_element = container.css('.date::text').get()
        if date_element:
            return date_element.strip()

        # Fallback: Look for datetime patterns in the container
        datetime_patterns = [
            r'(\d{4}\.\d{2}\.\d{2}\s+\d{2}:\d{2})',  # 2025.06.08 02:25
            r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2})',    # 2025-06-08 02:25
        ]

        all_text = ' '.join(container.css('*::text').getall())

        for pattern in datetime_patterns:
            match = re.search(pattern, all_text)
            if match:
                return match.group(1)

        return None

    def extract_detailed_ratings(self, container, response):
        """Extract detailed ratings from JavaScript tooltip functions"""
        # Look for createRatingTooltip patterns in onmouseover attributes
        onmouseover_elements = container.css('*[onmouseover*="createRatingTooltip"]')
        for element in onmouseover_elements:
            onmouseover_value = element.css('::attr(onmouseover)').get()
            if onmouseover_value:
                ratings = self._parse_rating_tooltip(onmouseover_value)
                if ratings:
                    return ratings

        # Look for rating values in the container structure
        # The API response might have rating values in specific elements
        rating_elements = container.css('.rating *::text').getall()
        for text in rating_elements:
            if '[' in text and ']' in text:
                try:
                    # Extract array-like pattern [5.0,5.0,5.0]
                    match = re.search(r'\[([^\]]+)\]', text)
                    if match:
                        values_str = match.group(1)
                        values = [float(x.strip()) for x in values_str.split(',')]
                        if len(values) >= 3:
                            return values[:3]
                except (ValueError, IndexError):
                    continue

        return None

    def _parse_rating_tooltip(self, tooltip_text):
        """Parse rating values from createRatingTooltip function"""
        tooltip_match = re.search(r'createRatingTooltip\([^,]+,\s*\[([^\]]+)\]\)', tooltip_text)
        if tooltip_match:
            try:
                values_str = tooltip_match.group(1)
                values = [float(x.strip()) for x in values_str.split(',')]
                if len(values) >= 3:
                    return values[:3]
            except (ValueError, IndexError):
                pass
        return None

    def check_has_answer(self, container, response, review_id):
        """Check if review has a developer answer"""
        # Look for reply containers with matching review ID
        reply_selector = f'div[id="reply_{review_id}"]'
        reply_container = response.css(reply_selector)
        return bool(reply_container)

    def extract_answer_content(self, container, response, review_id):
        """Extract developer answer content"""
        reply_selector = f'div[id="reply_{review_id}"]'
        reply_container = response.css(reply_selector).first()

        if reply_container:
            # Extract text content from reply
            reply_texts = reply_container.css('*::text').getall()
            reply_content = ' '.join(reply_texts).strip()
            reply_content = re.sub(r'\s+', ' ', reply_content)
            return html.unescape(reply_content) if reply_content else ""

        return ""
