[{"review_id": "38658708", "review_datetime": "2025.06.12 15:58", "reviewer_name": "<PERSON><PERSON>", "reviewer_id": "tiltedk", "reviewer_score": 19, "review_content": "Backtest results correlate very well with live performance. Owner is super responsive and the Quantum community is supportive and helpful as well. This has been an excellent experience. Bought it on late May and profit has already covered the cost of this EA (including VPS cost). If you manage your risk and be patient, it just pays itself off. This is as close you can get to an golden goose.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.374834"}, {"review_id": "38766955", "review_datetime": "2025.06.12 11:50", "reviewer_name": "<PERSON><PERSON>", "reviewer_id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reviewer_score": 36, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.377173"}, {"review_id": "38826636", "review_datetime": "2025.06.12 04:27", "reviewer_name": "chalerm petcharat", "reviewer_id": "chale<PERSON><PERSON><PERSON><PERSON>", "reviewer_score": 53, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.379267"}, {"review_id": "38655413", "review_datetime": "2025.06.11 09:21", "reviewer_name": "<PERSON>", "reviewer_id": "pablo1904", "reviewer_score": 599, "review_content": "ich nutze den EA jetzt für ca. 2 Wochen, bislang ist er unaufgeregt. Das bedeutet, der macht wenige aber dafür profitabele Trades, was meinem Trading Stil entgegenkommt. Der Support arbeitet super schnell...", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.381660"}, {"review_id": "********", "review_datetime": "2025.06.11 08:19", "reviewer_name": "Kirilka59", "reviewer_id": "kirilka59", "reviewer_score": 36, "review_content": "I decided to leave a review after 2-3 weeks of testing on a live account. I really like the way QQ5 works. Deals are opened not very often, but qualitatively and I understand that this is due to the fact that EA waits for the perfect moment, taking into account the market situation. And when it opens a deal, then another one and another and eventually closes almost all deals with a profit. I have never had a series of trades that went into the minus, always in the plus. My settings are automatic with medium risk. My broker is ByBit. I am very happy with the result of QQ5's work, I will continue to trust him to manage my budget. Thank you for such a quality EA! I recommend everyone to buy it, the price matches the quality - and it will definitely pay off!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.383750"}, {"review_id": "********", "review_datetime": "2025.06.08 02:25", "reviewer_name": "goateeeth maker", "reviewer_id": "goateeeth", "reviewer_score": 48, "review_content": "⭐⭐⭐⭐⭐ Excellent EA—Profitable and Reliable! I've been using this EA on MT5 for several weeks, and it's exceeded my expectations. The performance has been consistent, generating steady profits while effectively managing risks. Setup was straightforward, and customer support has been responsive whenever I had questions. Highly recommend this EA to anyone looking for an automated, stress-free trading solution!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.385761"}, {"review_id": "********", "review_datetime": "2025.06.06 23:02", "reviewer_name": "Saman83", "reviewer_id": "saman83", "reviewer_score": 79, "review_content": "🌟 An Expert Advisor That Truly Delivers! 🌟 I’ve been using Quantum Queen MT5 for a short time, but I’m already seeing my account grow steadily. A colleague of mine has been using it for over two months, and his results have been exceptional and consistent, without hesitation. 🔹 It’s one of the very few algobots on MetaTrader 5 that actually works. 🔹 The developer is very professional and always quick to respond to any questions. 🔹 It was recommended to me by someone extremely meticulous… and it has exceeded all expectations. 💰 It’s worth every cent of the price. This is a professional tool, not just another empty promise. 🔒 Reliable, profitable, and well-supported. Highly recommended!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.387939"}, {"review_id": "********", "review_datetime": "2025.06.06 22:12", "reviewer_name": "SAMUEL DE SOUZA", "reviewer_id": "swtsam2", "reviewer_score": 53, "review_content": "Absolutely Phenomenal – A True Queen in the Game! I’ve tested over 40 different AEs in my trading journey, and I can confidently say the Quantum Queen MT5 is in a league of its own. It’s not just another gold (XAUUSD) AE—it’s a precision-engineered powerhouse. From setup to execution, everything runs smoothly and efficiently, adapting perfectly to changing market conditions. Alongside The Emperor, Quantum Queen is easily one of the two best AEs I’ve ever come across. The consistency, reliability, and intelligent trade management this bot brings are unmatched. Whether you’re a seasoned trader or just starting out, this is a must-have for your arsenal. Highly recommend to anyone serious about mastering gold on MT5!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.389975"}, {"review_id": "38705502", "review_datetime": "2025.06.06 20:33", "reviewer_name": "Noalc", "reviewer_id": "noalc", "reviewer_score": 22, "review_content": "购买这个EA之后我基于最近半年短期做了很多次回测 根据账户余额还有自己能承受的风险程度去调节不同的开仓方式 每一次都是盈利的 而且都是非常可观的 用我们国家的一句老话来说就是 兵欲善其事 必先利其器 我觉得QQ对我这个新手来说十分友好 并且我能控制我所能付出的来盈利更稳定的或者更多的 是一个非常好的", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.394544"}, {"review_id": "38679597", "review_datetime": "2025.06.06 02:24", "reviewer_name": "Dnamron", "reviewer_id": "d<PERSON><PERSON>", "reviewer_score": 66, "review_content": "Bonjour je viens d'acheter expert Quantum Queen MT5 c'est un très bon expert il est très stable il n'y a des perte que très minime et donne plus de bon profit je le trouve excellent. Très bon service après vente pour l'installation et les conseils de Bogdan Ion Pus<PERSON>u il a répondu à mes demandes et à mes messages avec une rapidité exemplaire je suis très satisfait merci beaucoup.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.396803"}, {"review_id": "38661377", "review_datetime": "2025.06.04 08:33", "reviewer_name": "jza80spl", "reviewer_id": "jza80spl", "reviewer_score": 19, "review_content": "\"This is an EA that allows you to trade with great peace of mind.\"", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.400108"}, {"review_id": "38691977", "review_datetime": "2025.06.03 16:06", "reviewer_name": "<PERSON><PERSON>", "reviewer_id": "tokio147", "reviewer_score": 378, "review_content": "Very good grid system. Thanks <PERSON><PERSON><PERSON>！", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.404038"}, {"review_id": "38596644", "review_datetime": "2025.06.02 21:18", "reviewer_name": "<PERSON><PERSON><PERSON>", "reviewer_id": "molobamllr", "reviewer_score": 24, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.409242"}, {"review_id": "38600643", "review_datetime": "2025.06.02 13:36", "reviewer_name": "<PERSON><PERSON><PERSON>", "reviewer_id": "nasoo", "reviewer_score": 785, "review_content": "Bought the EA last week and already made about 4% in just one week! Really happy with the performance so far. The documentation provided is very detailed and informative — it made setup and understanding the strategy very easy. Looking forward to seeing more great results.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.412761"}, {"review_id": "38580848", "review_datetime": "2025.06.01 15:52", "reviewer_name": "Francesc00", "reviewer_id": "francesc00", "reviewer_score": 34, "review_content": "I am very satisfied with the results from the 1st week, its the same as in the backtests. I fully recommend it.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.415889"}, {"review_id": "38668358", "review_datetime": "2025.05.31 06:14", "reviewer_name": "Extoflex", "reviewer_id": "schnitzler88", "reviewer_score": 328, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.420761"}, {"review_id": "38537336", "review_datetime": "2025.05.30 09:43", "reviewer_name": "<PERSON>", "reviewer_id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reviewer_score": 40, "review_content": "O Bogdan é um Trader extremamente responsável e simpático e oferece suporte rápido, a Quantum Queen é uma ótima EA que já me rendeu bons lucros com baixos riscos, recomendo fortemente o uso dessa EA para Traders responsáveis e que queiram fazer seu dinheiro render sem muito esforço", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.423905"}, {"review_id": "38237707", "review_datetime": "2025.05.30 08:56", "reviewer_name": "<PERSON>", "reviewer_id": "chasuhun23", "reviewer_score": 56, "review_content": "<PERSON><PERSON><PERSON> has been incredibly responsive, often replying within minutes, which makes it feel like I have a tech team right beside me — something I really appreciate. I’ve been running the EA from May 26th to May 29th, and it delivered a 2% profit within just four days using the default risk settings. It doesn’t use a martingale strategy, but it does attempt to average price levels in a controlled manner. I especially like that it only executes trades when specific market conditions are met, aiming for higher probability setups. So far, the performance has been solid and feels safe. Highly recommended, and based on these results, I plan to explore more of their products.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.427340"}, {"review_id": "38508546", "review_datetime": "2025.05.29 17:57", "reviewer_name": "<PERSON>", "reviewer_id": "m_schnitzer", "reviewer_score": 24, "review_content": "I can highly recommend the Queen. You get an immediate response to any kind of question, and the system truly works. Thank you, <PERSON><PERSON><PERSON>!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.432259"}, {"review_id": "38517999", "review_datetime": "2025.05.29 12:33", "reviewer_name": "Steels is", "reviewer_id": "steelis", "reviewer_score": 35, "review_content": "\"It is a great system and a profitable, thank you <PERSON><PERSON><PERSON>!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=1", "scraped_at": "2025-06-13T16:42:24.435692"}, {"review_id": "36154117", "review_datetime": "2025.01.02 16:26", "reviewer_name": "DieNastie", "reviewer_id": "<PERSON><PERSON><PERSON>", "reviewer_score": 46, "review_content": "I have been testing QQ for just over a month now. This EA is so far the most confident one that I bought. With a profit of just under 7% it easily fulfills my monthly target and that with a almost non-existent DD of a measly 1.5%", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=6", "scraped_at": "2025-06-13T16:42:27.380956"}, {"review_id": "36797236", "review_datetime": "2025.02.05 08:04", "reviewer_name": "jjdf", "reviewer_id": "jdfuentes12", "reviewer_score": 97, "review_content": "I started using Quantum Queen this January and was eager to see how well it performed. To truly gauge its potential, I conducted a thorough backtest for 2022-2024, and the results were nothing short of impressive. The win rate was incredibly high, exceeding my expectations. Beyond its performance, what truly stands out is the team behind it. The admins are not only passionate about their work but also deeply committed to transparency and integrity. It’s rare to find developers who genuinely care about creating an EA that delivers real results rather than just selling hype. Their dedication to refining and improving Quantum Queen is evident, making it a tool worth considering for anyone serious about trading.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.391048"}, {"review_id": "36265561", "review_datetime": "2025.02.04 16:59", "reviewer_name": "<PERSON>", "reviewer_id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reviewer_score": 187, "review_content": "Very excellent Relax and enjoy with Quantum", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.394144"}, {"review_id": "********", "review_datetime": "2025.02.04 09:32", "reviewer_name": "Pawel Lukasz Czyzyk", "reviewer_id": "dropauto", "reviewer_score": 202, "review_content": "I have been testing this expert on accounts of several brokers for over a month. I will only add that this is a very difficult time for gold (<PERSON> ;). The bot does not randomly issue positions. You can see that it patiently waits for an opportunity. My account balance confirms that these are good decisions. <PERSON><PERSON><PERSON> helps with configuration and answered all my questions. So I can really recommend the <PERSON><PERSON> and his product.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.397621"}, {"review_id": "********", "review_datetime": "2025.02.03 13:18", "reviewer_name": "MrDr Wave", "reviewer_id": "blechner750", "reviewer_score": 157, "review_content": "The QQ is a good EA. It's a grid trader, so it does pose an obvious risk. The signal account had only a 15% maximum DD for the longest time, until just hitting 30% in January. Two of it's five strategies increase lot size as the grid adds trades, that's where the real risk comes from. It's a good idea to set risk low, and make sure the equity is available. I set it lower than the very low automatic risk setting. But by the Queen's good graces, it closes its grids in profit. Every time so far. The Queen does not trade frequently. It can go a week or more without a trade. Or sometimes it might trade multiple times a day. It chooses its trading times carefully. Overall, it's a good EA. Produces good profits for a little risk. Hard to beat the Queen", "review_has_answer": false, "review_answer": "", "rating_description_quality": 4.0, "rating_reliability": 4.0, "rating_support": 4.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.399994"}, {"review_id": "36855303", "review_datetime": "2025.02.03 03:12", "reviewer_name": "riiiin", "reviewer_id": "riiiin", "reviewer_score": 134, "review_content": "I used it for 15 days. I am satisfied with the result. As long as you manage risks properly, you can easily increase your funds.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.403109"}, {"review_id": "36287902", "review_datetime": "2025.02.02 18:20", "reviewer_name": "thomaslampe65", "reviewer_id": "thomaslampe65", "reviewer_score": 258, "review_content": "I cannot say enough about QQ. I started on 12/12/24 and had a return of 4.68% by 12/18/24. The EA was between 12/20/24 and 1/10/25. Between 1/10/25 and 1/31/25 it returned 11.83%, 5.65%, and 1.45% respectively in the following three weeks - all at a low DD. Broker is OS Securities, Risk Level is Medium, LS 0.01 per $1,000.00. <PERSON><PERSON><PERSON>'s support is top-notch, I am wondering if this guy ever sleeps.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.406162"}, {"review_id": "36939227", "review_datetime": "2025.01.30 13:53", "reviewer_name": "<PERSON><PERSON><PERSON><PERSON>", "reviewer_id": "busgosur", "reviewer_score": 349, "review_content": "Very profitable and secure!!!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.409875"}, {"review_id": "36086152", "review_datetime": "2025.01.30 12:17", "reviewer_name": "<PERSON><PERSON><PERSON>", "reviewer_id": "ziad<PERSON><PERSON>l", "reviewer_score": 93, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.413040"}, {"review_id": "36937156", "review_datetime": "2025.01.26 17:20", "reviewer_name": "SparkyE", "reviewer_id": "sparkye", "reviewer_score": 105, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.416097"}, {"review_id": "36507013", "review_datetime": "2025.01.26 07:47", "reviewer_name": "<PERSON><PERSON><PERSON><PERSON>", "reviewer_id": "<PERSON><PERSON><PERSON>", "reviewer_score": 29, "review_content": "From 2 weeks I used Quantum Queen. this is one of the best EA i had and I made profit 30%. I will become one of Quantum Family. Thank you <PERSON><PERSON><PERSON> for made a good EA.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.419240"}, {"review_id": "36597763", "review_datetime": "2025.01.24 17:25", "reviewer_name": "140263", "reviewer_id": "140263", "reviewer_score": 109, "review_content": "I purchased Quantum Queen a couple of weeks ago and must honestly say, the performance has been incredible. Fascinating how the grid automatically adjusts to recover losing trades and recover the combined result in a win!!! Another great product Bogdan....thank you!!!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.423683"}, {"review_id": "36800534", "review_datetime": "2025.01.24 03:07", "reviewer_name": "<PERSON>", "reviewer_id": "<PERSON><PERSON><PERSON><PERSON>", "reviewer_score": 300, "review_content": "I recently purchased this Expert Advisor (QQ EA) and I can honestly say it has transformed my trading experience. I can even sleep peacefully at night, knowing that my investments are in good hands. This EA truly excels at compounding returns, making it a practical tool for any trader. So far, my results have been promising, and I appreciate its user-friendly interface and reliable performance. I’m excited to see how it continues to perform in the long run!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.426834"}, {"review_id": "36620178", "review_datetime": "2025.01.19 08:58", "reviewer_name": "DLPT", "reviewer_id": "dlpt", "reviewer_score": 47, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.429915"}, {"review_id": "36696905", "review_datetime": "2025.01.18 13:48", "reviewer_name": "<PERSON>", "reviewer_id": "dabax24", "reviewer_score": 134, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.432934"}, {"review_id": "36507459", "review_datetime": "2025.01.17 04:50", "reviewer_name": "<PERSON>", "reviewer_id": "taapoli", "reviewer_score": 219, "review_content": "I've been using QQ for some time now and the results are excellent! Contact <PERSON><PERSON><PERSON> now and ask a question and in less than 5 minutes, you have a comprehensive response from him. His response will also help you to learn about the behaviour of the product. Keep it up, <PERSON><PERSON><PERSON>.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.437987"}, {"review_id": "34866022", "review_datetime": "2025.01.15 06:16", "reviewer_name": "HaAP_1", "reviewer_id": "haap_1", "reviewer_score": 109, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.441074"}, {"review_id": "36727371", "review_datetime": "2025.01.14 10:21", "reviewer_name": "<PERSON><PERSON>", "reviewer_id": "jacobo47", "reviewer_score": 190, "review_content": "Yo le doy 5 porq estoy muy contento con el resultado q he visto de Quantum queen hasta ahorita y sobre todo por el excelente servicio q he recibido por parte de bogdan q todas las preguntas y dudas siempre contesta rapido y de manera atenta", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.444388"}, {"review_id": "34165704", "review_datetime": "2025.01.09 20:07", "reviewer_name": "<PERSON><PERSON><PERSON>", "reviewer_id": "humbertobc", "reviewer_score": 157, "review_content": "Hi, I have been using Quantum Queen for about 5 months now and the EA has been working well. I am happy with results, usability and support.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.449369"}, {"review_id": "36590175", "review_datetime": "2025.01.05 01:03", "reviewer_name": "<PERSON>", "reviewer_id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reviewer_score": 112, "review_content": "I've been using the Quantum Queen EA by <PERSON><PERSON><PERSON> for about a week now, and I couldn't be happier with my decision to make this purchase! This EA has truly transformed my trading experience, especially with gold (XAUUSD), though it works very well on several other pairs as well. The grid trading strategy it employs seems to be both innovative and effective. The setup process was straightforward, and the user-friendly interface made it easy to configure according to my trading style. I've seen consistent results in my backtesting and live trading which is up about 30% this week. The built-in risk management features are a huge plus, so far no draw downs of any significance. What I appreciate most is the support from <PERSON><PERSON><PERSON>. He has been very responsive to my questions. He seems dedicated to improving the EA and so far has been available to answer all my questions. Though I am new to it, the community around Quantum Queen EA also seems to be fantastic, with many users sharing tips and strategies. If you're looking for a reliable and effective trading tool, I highly recommend Quantum Queen EA. It's been a game-changer for me! I will be getting more of the Bodgan's lineup of the Quantum Expert Advisors. Thank you <PERSON><PERSON><PERSON>.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.452871"}, {"review_id": "36513973", "review_datetime": "2025.01.02 23:19", "reviewer_name": "<PERSON><PERSON>", "reviewer_id": "darekt", "reviewer_score": 216, "review_content": "I've been using Quantum Queen for over 1.5 weeks, I'm happy with what I see. I recommend it to anyone who can't decide. After a month I'll try to post photos of the transaction. Thx.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=5", "scraped_at": "2025-06-13T16:42:28.456642"}, {"review_id": "37743051", "review_datetime": "2025.04.04 15:49", "reviewer_name": "Luftikus", "reviewer_id": "lufti<PERSON>", "reviewer_score": 19, "review_content": "I can only overlook a short period of time but in this time the EA has never let me down. 100% profitable trades. Let's hope it will continue like that.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.378654"}, {"review_id": "37496104", "review_datetime": "2025.04.01 10:49", "reviewer_name": "<PERSON>", "reviewer_id": "bushibaby", "reviewer_score": 128, "review_content": "This has been my second product from <PERSON><PERSON><PERSON> and I'm very happy. Only developer I can trust. The EA works great and <PERSON><PERSON><PERSON>, his team and the community will help you with anything you need.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.383211"}, {"review_id": "37715563", "review_datetime": "2025.03.31 06:12", "reviewer_name": "<PERSON><PERSON><PERSON>", "reviewer_id": "marketingguru", "reviewer_score": 205, "review_content": "I bought the bot a week ago, on Friday there were profitable transactions, I will test it for some time and increase the risks", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.390473"}, {"review_id": "********", "review_datetime": "2025.03.29 06:37", "reviewer_name": "wolfhound", "reviewer_id": "wolfhound", "reviewer_score": 19, "review_content": "My first EA purchase - Quantum Queen- Wow I'm so happy with our first day at 10% profit already! Awesome discord and support and prompt communication from creator. I conducted a back test for 2024-25 , and the results were solid, win rate was incredibly high, exceeding my expectations! We will sit back, let the EA do its work, and watch the account grow steadily ! Thankyou <PERSON> for your hard work!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.393971"}, {"review_id": "********", "review_datetime": "2025.03.27 19:30", "reviewer_name": "Marc1201", "reviewer_id": "marc1201", "reviewer_score": 24, "review_content": "I have being using this EA since the beginning of 2025. What I can say is that this EA is really amazing! The way it handle the trades, the risk management and so on. Also <PERSON><PERSON><PERSON> is very helpful and answers all your questions very fast.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.398147"}, {"review_id": "37249963", "review_datetime": "2025.03.27 07:51", "reviewer_name": "<PERSON><PERSON>", "reviewer_id": "justynanatalia", "reviewer_score": 32, "review_content": "Great EA and great customer service. <PERSON><PERSON><PERSON> is really helpful and answers all your questions. The EA is profitable and safe. You just have to be patient as it doesn't trade every day. But when it does, you can be sure you will make money.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.401643"}, {"review_id": "37711329", "review_datetime": "2025.03.25 16:19", "reviewer_name": "<PERSON>", "reviewer_id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reviewer_score": 162, "review_content": "This is the third product I’ve purchased from this developer, and once again, I’m impressed with the quality and performance of the tool. Every product is well-built, reliable, and delivers exactly as promised. The support is outstanding—always responsive and helpful. You can clearly see that the developer truly cares about his customers. Highly recommended!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.404858"}, {"review_id": "37708216", "review_datetime": "2025.03.25 15:06", "reviewer_name": "<PERSON>", "reviewer_id": "101283122", "reviewer_score": 191, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.409401"}, {"review_id": "37610288", "review_datetime": "2025.03.20 10:37", "reviewer_name": "dnbosiris83", "reviewer_id": "dnbosiris83", "reviewer_score": 32, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.413863"}, {"review_id": "37445244", "review_datetime": "2025.03.18 08:13", "reviewer_name": "PierG", "reviewer_id": "pierg", "reviewer_score": 114, "review_content": "It's the fourth EA in my Quantum arsenal, I used it in combo with QGE because the strategies combine and after two weeks of use with fixed lot, I'm ready to let it work in auto mode. I trust it. And the Discord channel is really useful to refine some settings, thanks to their administrators and the entire community.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.417848"}, {"review_id": "37229047", "review_datetime": "2025.03.05 07:52", "reviewer_name": "pistis", "reviewer_id": "pistis", "reviewer_score": 93, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.423891"}, {"review_id": "37184147", "review_datetime": "2025.03.03 13:16", "reviewer_name": "Pk Uptome", "reviewer_id": "uptomeipk", "reviewer_score": 55, "review_content": "I have been using it for 3 weeks. QQ EA is a safe and infrequent trading. The developer gave good instructions on how to install it.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.427642"}, {"review_id": "36102843", "review_datetime": "2025.02.21 08:03", "reviewer_name": "<PERSON>", "reviewer_id": "a0983944886", "reviewer_score": 87, "review_content": "我使用QQ已經兩個月了,這款EA非常的穩定,雖然不會每天都交易,但它每次交易都會擁有優質的判斷,非常推薦!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.431442"}, {"review_id": "36262646", "review_datetime": "2025.02.17 07:09", "reviewer_name": "<PERSON>", "reviewer_id": "king-fx", "reviewer_score": 802, "review_content": "I have been using QQ for a few weeks. The first set of trades led to an unusually large drawdown (57%) using Very high risk mode, but still closed out the set in profit. This is similar to backtest results from prior years, so use a lower risk level to minimise draw downs. Although trades are infrequent all trade sets have closed in profit. It can place grid trades fairly close together around $3 apart. As Gold can move $50 + in a day this could be a danger, but so far execution has been good along with support. Once I run this longer I will review again but this appears to be a solid EA.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.437815"}, {"review_id": "36449569", "review_datetime": "2025.02.15 09:30", "reviewer_name": "<PERSON><PERSON>", "reviewer_id": "8328475514", "reviewer_score": 125, "review_content": "I can share the opinions of every user who left a review. This is another outstanding EA from Bogdan.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.442512"}, {"review_id": "36522113", "review_datetime": "2025.02.15 04:17", "reviewer_name": "<PERSON>", "reviewer_id": "songzhang123", "reviewer_score": 287, "review_content": "I used it for 45 days. I am very happy with the result", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.445649"}, {"review_id": "36159585", "review_datetime": "2025.02.11 10:22", "reviewer_name": "<PERSON><PERSON><PERSON>", "reviewer_id": "<PERSON><PERSON><PERSON>", "reviewer_score": 169, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.450173"}, {"review_id": "********", "review_datetime": "2025.02.05 14:12", "reviewer_name": "<PERSON><PERSON>", "reviewer_id": "ballot16173", "reviewer_score": 36, "review_content": "I've been using Quantum Queen on a live account, and despite the low trade frequency, it consistently delivers profits even in challenging market conditions. If you value precision and reliability over frequent trades, this EA is a great choice!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.453708"}, {"review_id": "********", "review_datetime": "2025.02.05 09:02", "reviewer_name": "Massini28", "reviewer_id": "tom<PERSON>ing", "reviewer_score": 111, "review_content": "I bought the QQueen on January 9 and it has been running until February 5 and the results are great 10% profit!! you don't often have ongoing trades because it is a trend following strategy and the drawdown is very nice with the right risk setting! don't be too greedy, even at low risk the QQ achieves a very good result! 100% recommended EA", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.457765"}, {"review_id": "36266740", "review_datetime": "2025.02.05 08:29", "reviewer_name": "ESTARIX", "reviewer_id": "estarix", "reviewer_score": 102, "review_content": "QQ is very solid. It patiently waits for high quality set ups, which can take days to weeks. But when it wins, it usually wins big. Some traders will be impatient not seeing traders for more than a couple days. If this is you, QQ isn’t for you.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=4", "scraped_at": "2025-06-13T16:42:29.463316"}, {"review_id": "38323310", "review_datetime": "2025.05.07 10:00", "reviewer_name": "<PERSON><PERSON><PERSON><PERSON>", "reviewer_id": "noinid", "reviewer_score": 140, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.384226"}, {"review_id": "38237145", "review_datetime": "2025.05.07 00:28", "reviewer_name": "<PERSON><PERSON><PERSON><PERSON>", "reviewer_id": "<PERSON><PERSON><PERSON><PERSON>", "reviewer_score": 79, "review_content": "I've been using this EA for about a week now and the results are starting to show. the product looks promising", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.387674"}, {"review_id": "38192624", "review_datetime": "2025.05.06 10:14", "reviewer_name": "Sh74fy", "reviewer_id": "sh74fy", "reviewer_score": 30, "review_content": "i have been using this product for a couple of weeks now live and it works great and simple it make me profit :D", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.392246"}, {"review_id": "37637601", "review_datetime": "2025.05.05 05:00", "reviewer_name": "era0arms", "reviewer_id": "era0arms", "reviewer_score": 19, "review_content": "I have been running QQ since the end of March and have had excellent results. I have never used an EA with such stability and I strongly recommend it. More users will bring even greater profits. The support was very attentive and prompt, and helped me with various consultations even before I purchased the product. I appreciate it.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.395540"}, {"review_id": "37700753", "review_datetime": "2025.05.02 06:37", "reviewer_name": "scheiber.l", "reviewer_id": "scheiber.l", "reviewer_score": 596, "review_content": "The EA Quantum Queen has been working very stably and successfully for me for two months. I'm very satisfied.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.398944"}, {"review_id": "37778556", "review_datetime": "2025.05.01 13:53", "reviewer_name": "billtseng1995", "reviewer_id": "billtseng1995", "reviewer_score": 49, "review_content": "I started using Quantum Queen on March 28. Although I hit my preset stop loss at the beginning, it didn’t take long before I turned from a loss to a profit. I’m very satisfied and hope to achieve long-term gains. It wasn’t a perfect start, but that’s often how trading goes.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.404972"}, {"review_id": "36528198", "review_datetime": "2025.05.01 09:38", "reviewer_name": "<PERSON>", "reviewer_id": "neofxpro", "reviewer_score": 133, "review_content": "This is my first EA purchase from this developer, after 2 months monitoring performance it impressed me with quality entry and reliable profitable. Thanks for great service support", "review_has_answer": false, "review_answer": "", "rating_description_quality": 4.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.410025"}, {"review_id": "37730396", "review_datetime": "2025.04.30 12:09", "reviewer_name": "<PERSON>", "reviewer_id": "dinobox", "reviewer_score": 168, "review_content": "Llevo operando más de un mes con quamtum queen y decir que es una garantía de éxito. La atención de bogdan es sensacional", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.417143"}, {"review_id": "********", "review_datetime": "2025.04.28 06:32", "reviewer_name": "lase3388", "reviewer_id": "lase3388", "reviewer_score": 19, "review_content": "After 3 months of using on a real account $1000, this robot return about 70%. Very successful, I'm very confident in it. Thank you <PERSON><PERSON><PERSON> for this robot. Keep on the good work.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.421381"}, {"review_id": "********", "review_datetime": "2025.04.27 09:11", "reviewer_name": "<PERSON>", "reviewer_id": "qi<PERSON><PERSON>", "reviewer_score": 278, "review_content": "<PERSON> Queen is an amazing EA, one of <PERSON><PERSON><PERSON>'s best EAs, and I'm really proud of the consistency, profitability and reliability that QQ is having for me. Congrats <PERSON><PERSON><PERSON> for your success and efforts!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.426908"}, {"review_id": "********", "review_datetime": "2025.04.26 10:42", "reviewer_name": "Bitten", "reviewer_id": "lbitten", "reviewer_score": 36, "review_content": "So far so good, i bought also Quantum Emperor with the profits of the Quantum Queen from a $300 account in November, it works insanely good", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.432595"}, {"review_id": "********", "review_datetime": "2025.04.26 00:26", "reviewer_name": "<PERSON><PERSON><PERSON>rt<PERSON><PERSON>", "reviewer_id": "i<PERSON>_mart<PERSON><PERSON>", "reviewer_score": 153, "review_content": "The truth is that I am a fan of this programmer, I have already bought several of his EA's, and with \"Quantum Queen\" it is no exception, he makes very accurate entries with profits and very few losses, I highly recommend Quantum Queen and any of the EA's from this programmer.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.436575"}, {"review_id": "********", "review_datetime": "2025.04.25 09:46", "reviewer_name": "<PERSON>", "reviewer_id": "markus0202", "reviewer_score": 3089, "review_content": "I bought this robot one of the first and since then I have been testing and checking it on a demo account, now I am ready to install it for work on a real account. I want to say that so far it has not had a single losing trade and I hope that it will continue to be so. At the moment, this is one of the best robots on MQL5.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.440128"}, {"review_id": "********", "review_datetime": "2025.04.21 10:49", "reviewer_name": "<PERSON>", "reviewer_id": "tvrtiska", "reviewer_score": 120, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.444690"}, {"review_id": "********", "review_datetime": "2025.04.16 01:22", "reviewer_name": "<PERSON>", "reviewer_id": "par<PERSON>ne", "reviewer_score": 76, "review_content": "Wow! For 12 trading days my Quantum Queen runs quietly and delivered steady profit on my LiteForex account (it already made 10% on my $2k balance), so proud of this tool. It doesn’t rush trades, but when it does, it’s precise and reassuring. I feel safe letting it work for me. Thanks for the great work of <PERSON>gdan! Highly recommended for every Gold trader out there.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.448040"}, {"review_id": "********", "review_datetime": "2025.04.15 06:55", "reviewer_name": "<PERSON>", "reviewer_id": "<PERSON><PERSON><PERSON>", "reviewer_score": 250, "review_content": "I've been using the Quantum Queen for almost half a year now. I started with very low risk settings and have recently switched to fixed lots – that's how confident I am in this EA. I'm honestly extremely impressed! What I really appreciate is that the EA doesn’t trade every single day – it waits patiently for strong setups. But when it strikes, it really delivers. Those trades often bring in solid profits, and the strategy feels smart and calculated. I can highly recommend Quantum Queen to anyone looking for a calm, consistent, and highly effective EA. Patience truly pays off with this one!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.453666"}, {"review_id": "37569267", "review_datetime": "2025.04.09 12:05", "reviewer_name": "may kim", "reviewer_id": "mayone6401", "reviewer_score": 35, "review_content": "After experiencing copy trading with a friend of Quantum Emperor MT5, I bought a Quantum Queen myself and have been using it for 1 month...I'm very happy with the risk level being medium and doing very good returns in a volatile market with Trump tariffs. I strongly recommend Quantum Queen to traders. I'm sure you'll be very satisfied.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.459441"}, {"review_id": "********", "review_datetime": "2025.04.09 09:28", "reviewer_name": "Victor V.S.", "reviewer_id": "4175294", "reviewer_score": 45, "review_content": "I’ve been using Quantum Queen for a few months now (and other Quantum products since 2023), and I’m genuinely impressed with its performance! The EA delivers consistent results, and I’ve noticed steady growth in my account since I started. The precision and speed of the trades are outstanding, and the user-friendly interface makes it ideal for both beginners and experienced traders. <PERSON><PERSON><PERSON> and his team are incredibly supportive, always quick to respond and help with any questions. I highly recommend Quantum Queen to anyone looking for a reliable and efficient trading solution. Another great EA for my Quatum's EAs portfolio!! Thank you, <PERSON><PERSON><PERSON>, for this amazing product!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.463783"}, {"review_id": "********", "review_datetime": "2025.04.06 10:57", "reviewer_name": "psyraed", "reviewer_id": "psyraed", "reviewer_score": 35, "review_content": "Finally, after trying others, I managed to find <PERSON> with amazing results. I can forget about trading—every day I see profits. Amazing!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.477110"}, {"review_id": "36679107", "review_datetime": "2025.04.05 13:54", "reviewer_name": "gio <PERSON>io", "reviewer_id": "5029421673", "reviewer_score": 88, "review_content": "Quantum Queen is a product that has far exceeded my expectations. Its ability to combine precision, speed, and innovative features makes it an essential ally for anyone looking to take their experience to the next level. The attention to detail and user-friendly interface show that it is designed with every type of user in mind, from beginners to experts. Since I started using it, I have noticed a remarkable improvement in my results. Its performance is reliable and consistent, providing a significant competitive advantage in any situation. I am truly excited to recommend Quantum Queen to anyone looking for a powerful, efficient, and modern solution for their needs. It is a choice that does not disappoint!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=3", "scraped_at": "2025-06-13T16:42:30.484906"}, {"review_id": "38451092", "review_datetime": "2025.05.27 12:23", "reviewer_name": "MaK SiK", "reviewer_id": "simesime1", "reviewer_score": 64, "review_content": "Very good grid system. Thanks <PERSON><PERSON><PERSON>", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.383622"}, {"review_id": "38474924", "review_datetime": "2025.05.26 18:30", "reviewer_name": "<PERSON>", "reviewer_id": "lok323", "reviewer_score": 153, "review_content": "This is my second Quantum EAs, finally I decided to get another Quantum EA to complete my strategy pool. QQ is profitable EA and really good user support from author as usual and have great user community.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.387251"}, {"review_id": "38437276", "review_datetime": "2025.05.25 16:19", "reviewer_name": "BOSWANSIB", "reviewer_id": "b<PERSON><PERSON><PERSON><PERSON>", "reviewer_score": 63, "review_content": "hello, i'm from indonesia, i just used this EA for a week and i'm very satisfied with the performance and performance of Quantum Queen. you must try this EA, let the system do the work. greetings profit :)", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.393290"}, {"review_id": "38506204", "review_datetime": "2025.05.24 03:58", "reviewer_name": "toshi", "reviewer_id": "toshi<PERSON><PERSON>_homma", "reviewer_score": 501, "review_content": "I've only been using it for a week, but I've confirmed that the backtest and live trading match. I'm satisfied with the trading results in the first week, as they were as expected before I purchased it. The trading frequency was as expected, and no unnecessary transactions occurred, so I'm looking forward to the future. Thank you.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.396591"}, {"review_id": "38203818", "review_datetime": "2025.05.21 14:01", "reviewer_name": "Lamax888", "reviewer_id": "lamax888", "reviewer_score": 25, "review_content": "I bought this EA at the beginning of May after having already tested it in the previous months. The results are the same as the back test and I am surprised by how it manages the entries. On May 20th the EA entered at the end of the trading session and held the positions overnight. Not knowing how it would behave I did not go to bed to see what it did. It closed with a great profit taking advantage of the reasonable volatility at the beginning of the session, taking home a profit of 2% on the capital. Spectacular!! I confirm that <PERSON><PERSON><PERSON>'s support is excellent.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.400534"}, {"review_id": "********", "review_datetime": "2025.05.21 12:28", "reviewer_name": "<PERSON>", "reviewer_id": "<PERSON><PERSON><PERSON><PERSON>", "reviewer_score": 73, "review_content": "I have been trying this ea for more than a month on a live account and so far the results are good. The developer is very responsible in updating the ea according to market conditions. Two thumbs up for the developer!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.404794"}, {"review_id": "********", "review_datetime": "2025.05.20 15:57", "reviewer_name": "Senol", "reviewer_id": "tradersenol", "reviewer_score": 21, "review_content": "I tested QQ on demo for over a week and was pleased with the performance. It may take some time to start trading, so patience is important early on. I'm now moving to live trading and excited to see long-term results. Great job, <PERSON><PERSON><PERSON>!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.408763"}, {"review_id": "38414544", "review_datetime": "2025.05.19 12:13", "reviewer_name": "<PERSON><PERSON>", "reviewer_id": "boracngz", "reviewer_score": 128, "review_content": "I'm only using it for one week. I've done many tests before purchasing it and it is exactly the same on the demo. The profit I got from the first week is very satisfying. I also checked the previous updates of this EA and it is giving confidence for the future. Also <PERSON><PERSON><PERSON> is very helpful. Thanks.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.412242"}, {"review_id": "38059734", "review_datetime": "2025.05.18 18:34", "reviewer_name": "<PERSON>", "reviewer_id": "<PERSON><PERSON><PERSON><PERSON>", "reviewer_score": 286, "review_content": "I`ve Purchased Quantum Queen a month ago and it absolutly matches the great back testing. Trading live with QQ is really on another level. Creator support is very helpful 24-7 . An after the last update the Drawdown is extremely reduced and the EA got even more stable. Thanks to <PERSON><PERSON><PERSON>", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.417132"}, {"review_id": "38287856", "review_datetime": "2025.05.17 05:56", "reviewer_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reviewer_id": "pvrk80", "reviewer_score": 167, "review_content": "Hi all,I have purchased QQ in March after reading positive reviews from fellow traders. <PERSON><PERSON><PERSON> has done an incredible job in developing this EA which is evident in its functioning. It is a fact that you can do trading on your own and earn profits. The point here is that using such a good and reliable EA changes the way you spend your time as it takes care of trading in right conditions. Suppose you need to travel or sleeping,you don’t need to worry about the market,this EA will take care of it. This EA uses a systematic process to recover the adverse movements. I would definitely suggest everyone to try the demo and see yourself how this EA is working wonderfully,upon which you will purchase yourself. Good thing with this EA is that the quality of trades it opens.You may see choppy movements in market which are not good for trading so this EA knows how to identify them.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.421999"}, {"review_id": "38313891", "review_datetime": "2025.05.15 20:29", "reviewer_name": "<PERSON><PERSON>", "reviewer_id": "kiru2", "reviewer_score": 110, "review_content": "Absolute Beast of an EA, low dd quick profits", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.426511"}, {"review_id": "38219901", "review_datetime": "2025.05.15 16:48", "reviewer_name": "dots432", "reviewer_id": "dots432", "reviewer_score": 63, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.432451"}, {"review_id": "38338541", "review_datetime": "2025.05.15 10:37", "reviewer_name": "<PERSON><PERSON>", "reviewer_id": "sam8821", "reviewer_score": 175, "review_content": "I have just used it for a week, and although there have been few transactions, it is very stable. I hope to continue to make stable profits.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.437071"}, {"review_id": "38279280", "review_datetime": "2025.05.14 08:53", "reviewer_name": "nataworld", "reviewer_id": "nataworld", "reviewer_score": 88, "review_content": "While the QQ doesn’t trade constantly, the accuracy of its signals is impressive. It waits for high-probability setups, which means fewer but more reliable trades. The risk management is solid, and I’ve seen good trade profits already. Highly recommend this EA for anyone looking for a smart, patient trading bot.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.440060"}, {"review_id": "38351768", "review_datetime": "2025.05.13 19:36", "reviewer_name": "<PERSON><PERSON>", "reviewer_id": "cbarrios1990", "reviewer_score": 28, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.444884"}, {"review_id": "38390125", "review_datetime": "2025.05.12 02:34", "reviewer_name": "xy3586", "reviewer_id": "xy3586", "reviewer_score": 81, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.448682"}, {"review_id": "38320992", "review_datetime": "2025.05.10 05:28", "reviewer_name": "JCF", "reviewer_id": "jcfabul16", "reviewer_score": 80, "review_content": "Awesome EA! I started loving it now, accurate execution of trades. Hope this will be profitable in the long run! I love this EA!", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.452179"}, {"review_id": "38224567", "review_datetime": "2025.05.08 17:57", "reviewer_name": "shahbaz112", "reviewer_id": "shahbaz112", "reviewer_score": 130, "review_content": "“Following two weeks of active use, I am highly satisfied with the performance and reliability of Quantum Queen. Prior to purchasing, I conducted a thorough two-month evaluation of its trading behavior and was particularly impressed by its accurate entry points and steady profitability. Additionally, the developer’s responsive and professional support has been greatly appreciated throughout the process.” Thank you <PERSON><PERSON><PERSON>.", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.458625"}, {"review_id": "********", "review_datetime": "2025.05.08 01:53", "reviewer_name": "<PERSON><PERSON>", "reviewer_id": "faisalhossain2985", "reviewer_score": 44, "review_content": "Ran it with a Demo this EA excellent results as you can see just a week. Soon after additional weeks to go will switch to live account . Made almost $3200 with a 2 lot 100k account on demo 1 week only . Excellent EA", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.463100"}, {"review_id": "********", "review_datetime": "2025.05.07 10:12", "reviewer_name": "nonoM33", "reviewer_id": "nonom33", "reviewer_score": 78, "review_content": "User didn't leave any comment to the rating", "review_has_answer": false, "review_answer": "", "rating_description_quality": 5.0, "rating_reliability": 5.0, "rating_support": 5.0, "timestamp_extraction_review": "2025-06-13 16:42", "product_id": "118805", "url": "https://www.mql5.com/market/product/118805/reviews?filter=new&page=2", "scraped_at": "2025-06-13T16:42:31.467245"}]